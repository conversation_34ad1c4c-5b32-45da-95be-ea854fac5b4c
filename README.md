# 预测号码准确率统计系统

本系统提供两种核心的预测号码准确率统计模式，用于分析预测算法在不同维度上的表现。

## 📊 系统概述

### 数据源
- **输入文件**: 支持单个或多个CSV文件
  - 单文件模式: `real_time_data_20250817.csv`
  - 多文件模式: 自动扫描目录中的所有CSV文件
- **数据格式**: CSV文件，包含预测号码序列和开出号码
- **预测序列长度**: 9000个号码 (索引0-8999)
- **开出号码范围**: 1-8
- **多文件合并**: 自动合并多个文件的统计结果

### 核心功能
1. **索引位置准确率统计** - 分析每个具体索引位置的命中表现
2. **索引区域准确率统计** - 分析连续索引区域的整体命中表现

---

## 🎯 模式1: 索引位置准确率统计

### 文件信息
- **脚本文件**: `sorted_index_accuracy.py`
- **报告文件**: `sorted_index_accuracy_report.txt`

### 功能描述
统计预测号码列表中每个索引位置(0-8999)的准确率，并按准确率从高到低排序。

### 统计逻辑
```
对于每条记录:
  对于预测列表中的每个索引位置i:
    如果 predict_numbers[i] == actual_number:
      索引i的命中次数 += 1
    索引i的总出现次数 += 1

索引i的准确率 = 命中次数 / 总出现次数 * 100%
```

### 使用方法
```bash
python sorted_index_accuracy.py
```

### 输出示例
```
=== 各索引位置准确率统计（按准确率排序）===
排名 | 索引 | 总出现次数 | 命中次数 | 准确率
--------------------------------------------------
  1  | 1145 |     152      |    35    |  23.03%
  2  | 4692 |     152      |    34    |  22.37%
  3  | 7739 |     152      |    34    |  22.37%
  4  | 7972 |     152      |    34    |  22.37%
  5  |   78 |     152      |    33    |  21.71%
```

### 关键指标
- **最高准确率**: 23.03% (索引1145)
- **最低准确率**: 2.63% (索引6940)
- **平均准确率**: 12.53%
- **总索引位置数**: 9,000个

### 应用场景
- 识别表现最佳的具体索引位置
- 优化预测算法，重点关注高准确率索引
- 分析预测序列中的"热点"位置

---

## 🎯 模式2: 索引区域准确率统计

### 文件信息
- **脚本文件**: `flexible_region_analysis.py`
- **推荐报告**: `region_size_5_accuracy_report.txt`

### 功能描述
将9000个索引位置按指定大小划分成连续区域，统计每个区域的整体准确率。如果区域内任何一个索引位置命中开奖号码，该区域就算命中。

### 统计逻辑
```
区域大小 = N (可配置，推荐N=5)
区域数量 = 9000 / N = 1800个区域 (当N=5时)

对于每条记录:
  命中区域集合 = 空集合
  对于预测列表中的每个索引位置i:
    区域ID = i // N
    如果 predict_numbers[i] == actual_number:
      命中区域集合.add(区域ID)
  
  对于每个区域ID:
    区域的总记录数 += 1
    如果 区域ID in 命中区域集合:
      区域的命中次数 += 1

区域准确率 = 命中次数 / 总记录数 * 100%
```

### 使用方法
```bash
python flexible_region_analysis.py
```

### 区域大小对比
| 区域大小 | 总区域数 | 最高准确率 | 最低准确率 | 平均准确率 | 标准差 |
|----------|----------|------------|------------|------------|--------|
| **5**    | 1800     | **61.84%** | **35.53%** | **48.82%** | **4.03%** ⭐ |
| **10**   | 900      | **85.53%** | **61.84%** | **73.82%** | **3.67%** |
| **15**   | 600      | **93.42%** | **78.29%** | **86.60%** | **2.55%** |
| **20**   | 450      | **97.37%** | **85.53%** | **93.12%** | **2.01%** |
| **25**   | 360      | **99.34%** | **91.45%** | **96.44%** | **1.45%** |
| **30**   | 300      | **100.00%** | **95.39%** | **98.18%** | **1.03%** |

### 推荐配置
**最佳区域大小: 5个索引位置**

**选择理由:**
- ✅ 最大的区分度 (标准差4.03%)
- ✅ 合理的准确率范围 (35.53% - 61.84%)
- ✅ 足够的分析粒度 (1800个区域)
- ✅ 避免过度聚合效应

### 输出示例 (区域大小=5)
```
=== 各区域准确率统计（按准确率排序）===
排名 | 区域 | 索引范围      | 总记录数 | 命中次数 | 准确率
-----------------------------------------------------------------
  1  | 1128 | 5640-5644 |   152    |    94    |  61.84%
  2  |  937 | 4685-4689 |   152    |    93    |  61.18%
  3  | 1303 | 6515-6519 |   152    |    93    |  61.18%
  4  |  359 | 1795-1799 |   152    |    92    |  60.53%
  5  | 1715 | 8575-8579 |   152    |    92    |  60.53%
```

### 应用场景
- 识别表现最佳的索引区域
- 区域级别的预测策略优化
- 平衡精度和覆盖范围的分析

---

## 📋 文件结构

```
项目目录/
├── real_time_data_20250817.csv          # 原始数据文件
├── sorted_index_accuracy.py             # 索引位置统计脚本
├── flexible_region_analysis.py          # 索引区域统计脚本
├── sorted_index_accuracy_report.txt     # 索引位置统计报告
├── region_size_5_accuracy_report.txt    # 区域统计报告(推荐)
├── region_size_10_accuracy_report.txt   # 区域统计报告(大小10)
├── region_size_15_accuracy_report.txt   # 区域统计报告(大小15)
├── region_size_20_accuracy_report.txt   # 区域统计报告(大小20)
├── region_size_25_accuracy_report.txt   # 区域统计报告(大小25)
├── region_size_30_accuracy_report.txt   # 区域统计报告(大小30)
└── README.md                            # 本文档
```

---

## 🚀 快速开始

### 0. 配置多文件处理（可选）
编辑 `config.py` 设置数据处理模式：
```python
# 多文件模式（推荐）
DATA_MODE = 'multiple'
DATA_FILE_PATTERN = '*.csv'  # 处理所有CSV文件

# 单文件模式
DATA_MODE = 'single'
DATA_FILE = 'real_time_data_20250817.csv'
```

### 1. 测试文件发现
```bash
python test_multi_files.py
```
验证系统能正确发现数据文件

### 2. 运行索引位置统计
```bash
python sorted_index_accuracy.py
```
查看结果: `sorted_index_accuracy_report.txt`

### 3. 运行区域统计
```bash
python flexible_region_analysis.py
```
查看推荐结果: `region_size_5_accuracy_report.txt`

### 4. 分析结果
- 索引位置统计：找出表现最佳的具体索引
- 区域统计：找出表现最佳的索引区域
- 多文件合并：获得更大样本量的可靠统计
- 结合两种分析优化预测策略

---

## 📊 数据解读指南

### 准确率含义
- **索引准确率**: 该索引位置预测正确的概率
- **区域准确率**: 该区域内至少有一个位置预测正确的概率

### 性能基准
- **理论随机准确率**: 12.5% (1/8)
- **优秀表现**: >15%
- **卓越表现**: >20%

### 优化建议
1. **重点关注**: 准确率>20%的索引位置或区域
2. **策略调整**: 增加高准确率位置的权重
3. **持续监控**: 定期重新统计验证稳定性

---

## ⚠️ 注意事项

1. **数据完整性**: 确保CSV文件格式正确，预测序列完整
2. **内存使用**: 处理大文件时注意内存占用
3. **结果解释**: 准确率受样本数量影响，需要足够的历史数据
4. **策略应用**: 统计结果仅供参考，实际应用需要综合考虑多种因素

---

## 📞 技术支持

如有问题或需要定制化分析，请参考脚本中的详细注释或联系开发团队。
