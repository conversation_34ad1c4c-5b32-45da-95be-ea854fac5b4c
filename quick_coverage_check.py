#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速覆盖检查工具
快速验证100%准确率区域是否完全覆盖了1-8所有房间
"""

import csv
import re
import random
from collections import Counter
from typing import List, Dict, Set

def parse_csv_line(line: str) -> List[str]:
    """解析CSV行"""
    fields = []
    current_field = ""
    in_quotes = False
    bracket_count = 0
    
    i = 0
    while i < len(line):
        char = line[i]
        
        if char == '"' and (i == 0 or line[i-1] != '\\'):
            in_quotes = not in_quotes
            current_field += char
        elif char == '[':
            bracket_count += 1
            current_field += char
        elif char == ']':
            bracket_count -= 1
            current_field += char
        elif char == ',' and not in_quotes and bracket_count == 0:
            fields.append(current_field.strip())
            current_field = ""
        else:
            current_field += char
        
        i += 1
    
    if current_field:
        fields.append(current_field.strip())
    
    return fields

def generate_random_sequence(draw_timestamp_seconds: int) -> List[int]:
    """生成随机数序列"""
    first_seed = (draw_timestamp_seconds * 1000) - 5000
    
    random_sequence = []
    for i in range(9000):
        seed = first_seed + i
        random.seed(seed)
        result = random.randint(1, 8)
        random_sequence.append(result)
    
    return random_sequence

def check_region_coverage(start: int, end: int, sample_records: List[Dict]) -> Dict:
    """检查区域覆盖情况"""
    all_room_sets = []
    
    for record in sample_records:
        random_sequence = generate_random_sequence(record['draw_timestamp_seconds'])
        region_predictions = random_sequence[start:end+1]
        unique_rooms = set(region_predictions)
        all_room_sets.append(unique_rooms)
    
    # 分析覆盖情况
    always_covered = set(range(1, 9))
    sometimes_covered = set()
    
    for room_set in all_room_sets:
        always_covered &= room_set
        sometimes_covered |= room_set
    
    never_covered = set(range(1, 9)) - sometimes_covered
    
    return {
        'always_covered': always_covered,
        'sometimes_covered': sometimes_covered,
        'never_covered': never_covered,
        'total_coverage_rate': len(sometimes_covered) / 8 * 100,
        'always_coverage_rate': len(always_covered) / 8 * 100,
        'sample_room_sets': all_room_sets[:5]  # 前5个样本
    }

def main():
    """主函数"""
    print("🔍 快速覆盖检查工具")
    print("=" * 50)
    print("验证100%准确率区域是否完全覆盖1-8所有房间")
    print()
    
    # 加载少量数据进行快速检查
    csv.field_size_limit(2000000)
    
    records = []
    with open('real_time_data_20250817.csv', 'r', encoding='utf-8') as f:
        content = f.read()
        lines = content.strip().split('\n')
        
        for i, line in enumerate(lines[1:11], 1):  # 只取前10条记录
            try:
                fields = parse_csv_line(line)
                
                if len(fields) >= 7:
                    period = fields[1]
                    draw_timestamp = int(fields[2])
                    output_room = int(fields[6])
                    
                    records.append({
                        'period': period,
                        'draw_timestamp_seconds': draw_timestamp,
                        'actual_output_room': output_room
                    })
                    
            except Exception as e:
                continue
    
    print(f"✅ 加载了 {len(records)} 条测试记录")
    print()
    
    # 测试当前的100%准确率区域
    regions = [
        (2258, 2295, "区域A"),
        (2259, 2296, "区域B"),
        (4344, 4381, "区域C"),
        (4345, 4382, "区域D"),
        (4346, 4383, "区域E"),
    ]
    
    print("📊 区域覆盖分析:")
    print("-" * 50)
    
    all_regions_full_coverage = True
    
    for start, end, name in regions:
        coverage = check_region_coverage(start, end, records)
        
        print(f"{name} ({start}-{end}):")
        print(f"   区域大小: {end - start + 1}")
        print(f"   总是覆盖的房间: {sorted(list(coverage['always_covered']))}")
        print(f"   有时覆盖的房间: {sorted(list(coverage['sometimes_covered']))}")
        print(f"   从不覆盖的房间: {sorted(list(coverage['never_covered']))}")
        print(f"   总覆盖率: {coverage['total_coverage_rate']:.1f}%")
        print(f"   恒定覆盖率: {coverage['always_coverage_rate']:.1f}%")
        
        # 显示前几个样本的房间集合
        print(f"   样本房间集合:")
        for i, room_set in enumerate(coverage['sample_room_sets'], 1):
            print(f"     样本{i}: {sorted(list(room_set))}")
        
        # 检查是否完全覆盖
        if coverage['total_coverage_rate'] == 100.0:
            print(f"   ⚠️ 警告: 该区域完全覆盖所有房间1-8!")
        else:
            print(f"   ✅ 该区域有选择性覆盖")
            all_regions_full_coverage = False
        
        print()
    
    # 总结
    print("🎯 分析结论:")
    print("=" * 50)
    
    if all_regions_full_coverage:
        print("❌ 你的怀疑是正确的!")
        print("   所有100%准确率区域都完全覆盖了房间1-8")
        print("   这解释了为什么准确率是100% - 因为无论开什么房间都在预测范围内")
        print()
        print("💡 优化建议:")
        print("   1. 寻找更小的区域，只覆盖部分房间")
        print("   2. 寻找有明显房间偏好的区域")
        print("   3. 考虑使用单一最优区域而非多区域组合")
        print("   4. 重新分析统计数据，寻找真正有预测价值的区域")
    else:
        print("✅ 区域配置合理")
        print("   不是所有区域都完全覆盖1-8房间")
        print("   100%准确率是真实的预测能力")
    
    print()
    
    # 测试更小的区域
    print("🔧 测试更小区域的覆盖情况:")
    print("-" * 50)
    
    test_sizes = [5, 10, 15, 20]
    
    for size in test_sizes:
        print(f"\n测试区域大小: {size}")
        
        # 测试第一个区域的中心位置
        center = (2258 + 2295) // 2
        test_start = center - size // 2
        test_end = test_start + size - 1
        
        coverage = check_region_coverage(test_start, test_end, records)
        
        print(f"   位置: {test_start}-{test_end}")
        print(f"   覆盖房间: {sorted(list(coverage['sometimes_covered']))}")
        print(f"   覆盖率: {coverage['total_coverage_rate']:.1f}%")
        
        if coverage['total_coverage_rate'] < 100.0:
            print(f"   ✅ 找到有选择性的区域!")
            
            # 检查准确率
            successful = 0
            for record in records:
                random_sequence = generate_random_sequence(record['draw_timestamp_seconds'])
                region_predictions = random_sequence[test_start:test_end+1]
                if record['actual_output_room'] in region_predictions:
                    successful += 1
            
            accuracy = successful / len(records) * 100
            print(f"   准确率: {accuracy:.1f}%")
            
            if accuracy == 100.0:
                print(f"   🎯 发现优化区域: 大小{size}, 准确率100%, 但不完全覆盖!")

if __name__ == "__main__":
    main()
