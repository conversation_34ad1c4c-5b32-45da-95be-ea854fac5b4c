{"report": "================================================================================\n🔬 种子关系综合分析报告\n================================================================================\n\n📊 基本统计:\n  • 分析记录数: 230\n  • 找到真实种子的记录: 57032\n  • 涉及期号数: 8\n\n🕐 开奖时间戳与序列时间戳关系:\n  序列首个时间戳偏移量 (前5个):\n    • 偏移 1753668941629: 1 次\n    • 偏移 1753669028542: 1 次\n    • 偏移 1753669113457: 1 次\n    • 偏移 1753669195375: 1 次\n    • 偏移 1753669274296: 1 次\n  序列最后时间戳偏移量 (前5个):\n    • 偏移 1753668950628: 1 次\n    • 偏移 1753669037541: 1 次\n    • 偏移 1753669122456: 1 次\n    • 偏移 1753669204374: 1 次\n    • 偏移 1753669283295: 1 次\n\n🎯 真实种子偏移模式 (前10个):\n  • 开奖时间戳 + -616: 49 次命中\n  • 开奖时间戳 + -934: 47 次命中\n  • 开奖时间戳 + 867: 45 次命中\n  • 开奖时间戳 + 895: 44 次命中\n  • 开奖时间戳 + -358: 44 次命中\n  • 开奖时间戳 + -494: 43 次命中\n  • 开奖时间戳 + 385: 43 次命中\n  • 开奖时间戳 + 763: 43 次命中\n  • 开奖时间戳 + 48: 43 次命中\n  • 开奖时间戳 + -567: 43 次命中\n\n📏 随机数序列长度分析:\n  • 长度 9000: 230 次\n\n💡 关键发现:\n  1. 最可能的真实种子生成规律:\n     真实种子 = 开奖时间戳 + -616\n     (在 49 次测试中命中)\n  2. 序列种子与真实种子重叠情况:\n     0/8 个期号存在重叠\n     重叠率: 0.0%\n  3. 实用建议:\n     • 使用 '开奖时间戳 + -616' 作为真实种子\n     • 该种子生成的随机数即为开奖结果\n     • 避开该随机数可提高获胜概率\n================================================================================", "statistics": {"total_records": 230, "true_seed_matches": 57032, "unique_periods_with_true_seeds": 8}, "true_offset_patterns": [[-616, 49], [-934, 47], [867, 45], [895, 44], [-358, 44], [-494, 43], [385, 43], [763, 43], [48, 43], [-567, 43]], "first_offset_patterns": [[1753668941629, 1], [1753669028542, 1], [1753669113457, 1], [1753669195375, 1], [1753669274296, 1], [1753669363207, 1], [1753669448122, 1], [1753669530040, 1], [1753669616953, 1], [1753669693876, 1]], "sequence_length_patterns": [[9000, 230]]}