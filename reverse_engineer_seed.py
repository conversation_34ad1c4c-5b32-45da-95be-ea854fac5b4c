#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
种子算法逆向工程工具
基于100%准确率的滑动窗口区域来推导真实的种子算法
"""

import csv
import ast
import random
from typing import List, Dict, Tuple, Set
from collections import defaultdict
import json

class SeedReverseEngineer:
    """种子算法逆向工程器"""
    
    def __init__(self):
        # 100%准确率的滑动窗口区域（来自region_size_38_accuracy_report.txt）
        self.perfect_regions = [
            {'start': 2258, 'end': 2295, 'accuracy': 100.00},  # 区域2258-2295
            {'start': 2259, 'end': 2296, 'accuracy': 100.00},  # 区域2259-2296
            {'start': 4344, 'end': 4381, 'accuracy': 100.00},  # 区域4344-4381
            {'start': 4345, 'end': 4382, 'accuracy': 100.00},  # 区域4345-4382
            {'start': 4346, 'end': 4383, 'accuracy': 100.00},  # 区域4346-4383
        ]
        
        self.lottery_data = []
        self.seed_analysis_results = {}
    
    def load_lottery_data(self, csv_file: str) -> bool:
        """
        加载开奖数据

        Args:
            csv_file: CSV文件路径

        Returns:
            是否加载成功
        """
        try:
            # 增加CSV字段大小限制
            csv.field_size_limit(1000000)

            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    # 解析随机数序列对应的时间戳
                    timestamp_list_str = row['随机数序列对应的时间戳']
                    timestamp_list = ast.literal_eval(timestamp_list_str)
                    
                    # 解析随机数序列
                    random_sequence_str = row['随机数序列']
                    random_sequence = ast.literal_eval(random_sequence_str)
                    
                    lottery_record = {
                        'period': row['期号'],
                        'draw_timestamp': int(row['开奖时间戳']),
                        'timestamp_list': timestamp_list,
                        'random_sequence': random_sequence,
                        'input_room': int(row['投入的房间']),
                        'output_room': int(row['开出的房间'])
                    }
                    
                    self.lottery_data.append(lottery_record)
            
            print(f"✅ 成功加载 {len(self.lottery_data)} 条开奖记录")
            return True
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return False
    
    def analyze_perfect_regions(self) -> Dict:
        """
        分析100%准确率区域的种子模式
        
        Returns:
            分析结果字典
        """
        analysis_results = {}
        
        for region in self.perfect_regions:
            region_key = f"{region['start']}-{region['end']}"
            print(f"\n🔍 分析区域 {region_key}...")
            
            region_analysis = {
                'region': region,
                'seed_patterns': [],
                'room_mappings': {},
                'algorithm_candidates': []
            }
            
            # 对每条开奖记录分析该区域
            for record in self.lottery_data:
                timestamp_list = record['timestamp_list']
                random_sequence = record['random_sequence']
                output_room = record['output_room']
                
                # 提取该区域的种子和对应的随机数
                region_seeds = []
                region_randoms = []
                
                for i in range(region['start'], region['end'] + 1):
                    if i < len(timestamp_list) and i < len(random_sequence):
                        seed = timestamp_list[i]
                        random_num = random_sequence[i]
                        region_seeds.append(seed)
                        region_randoms.append(random_num)
                
                # 分析种子到随机数的映射关系
                seed_pattern = {
                    'period': record['period'],
                    'output_room': output_room,
                    'seeds': region_seeds,
                    'randoms': region_randoms,
                    'seed_to_random_map': dict(zip(region_seeds, region_randoms))
                }
                
                region_analysis['seed_patterns'].append(seed_pattern)
                
                # 统计房间号映射
                if output_room not in region_analysis['room_mappings']:
                    region_analysis['room_mappings'][output_room] = []
                region_analysis['room_mappings'][output_room].append({
                    'period': record['period'],
                    'seeds': region_seeds,
                    'randoms': region_randoms
                })
            
            analysis_results[region_key] = region_analysis
        
        self.seed_analysis_results = analysis_results
        return analysis_results
    
    def find_seed_algorithm_patterns(self) -> Dict:
        """
        寻找种子算法模式
        
        Returns:
            算法模式分析结果
        """
        algorithm_patterns = {}
        
        for region_key, region_data in self.seed_analysis_results.items():
            print(f"\n🧮 分析区域 {region_key} 的算法模式...")
            
            patterns = {
                'direct_mapping': {},  # 直接映射：种子 -> 随机数
                'modulo_patterns': {},  # 取模模式
                'offset_patterns': {},  # 偏移模式
                'unity_random_test': {}  # Unity Random测试
            }
            
            # 收集所有种子到随机数的映射
            all_mappings = {}
            for pattern in region_data['seed_patterns']:
                for seed, random_num in pattern['seed_to_random_map'].items():
                    if seed not in all_mappings:
                        all_mappings[seed] = []
                    all_mappings[seed].append(random_num)
            
            # 分析直接映射模式
            consistent_mappings = {}
            for seed, randoms in all_mappings.items():
                if len(set(randoms)) == 1:  # 同一种子总是产生相同随机数
                    consistent_mappings[seed] = randoms[0]
            
            patterns['direct_mapping'] = consistent_mappings
            
            # 测试Unity Random算法
            unity_matches = 0
            unity_total = 0
            
            for seed, expected_random in consistent_mappings.items():
                # 测试Unity Random.Range(1, 9)
                random.seed(seed)
                unity_result = random.randint(1, 8)
                
                unity_total += 1
                if unity_result == expected_random:
                    unity_matches += 1
            
            if unity_total > 0:
                unity_accuracy = (unity_matches / unity_total) * 100
                patterns['unity_random_test'] = {
                    'matches': unity_matches,
                    'total': unity_total,
                    'accuracy': unity_accuracy
                }
            
            # 分析取模模式
            modulo_results = {}
            for modulo in [8, 9, 10, 16, 32]:
                modulo_matches = 0
                for seed, expected_random in consistent_mappings.items():
                    calculated = (seed % modulo) + 1
                    if calculated == expected_random or (calculated > 8 and expected_random <= 8):
                        modulo_matches += 1
                
                if len(consistent_mappings) > 0:
                    modulo_accuracy = (modulo_matches / len(consistent_mappings)) * 100
                    modulo_results[modulo] = {
                        'matches': modulo_matches,
                        'total': len(consistent_mappings),
                        'accuracy': modulo_accuracy
                    }
            
            patterns['modulo_patterns'] = modulo_results
            
            algorithm_patterns[region_key] = patterns
        
        return algorithm_patterns
    
    def test_custom_algorithms(self) -> Dict:
        """
        测试自定义算法
        
        Returns:
            自定义算法测试结果
        """
        custom_results = {}
        
        # 定义多种可能的算法
        algorithms = {
            'unity_python_random': lambda seed: self._unity_python_random(seed),
            'simple_modulo_8': lambda seed: (seed % 8) + 1,
            'simple_modulo_9': lambda seed: (seed % 9) + 1,
            'hash_based': lambda seed: self._hash_based_random(seed),
            'linear_congruential': lambda seed: self._linear_congruential(seed),
            'mersenne_twister': lambda seed: self._mersenne_twister_like(seed),
        }
        
        for region_key, region_data in self.seed_analysis_results.items():
            print(f"\n🧪 测试区域 {region_key} 的自定义算法...")
            
            region_results = {}
            
            # 收集测试数据
            test_cases = []
            for pattern in region_data['seed_patterns']:
                for seed, expected_random in pattern['seed_to_random_map'].items():
                    test_cases.append((seed, expected_random))
            
            # 测试每种算法
            for algo_name, algo_func in algorithms.items():
                matches = 0
                total = len(test_cases)
                
                for seed, expected in test_cases:
                    try:
                        result = algo_func(seed)
                        if result == expected:
                            matches += 1
                    except:
                        pass
                
                accuracy = (matches / total * 100) if total > 0 else 0
                region_results[algo_name] = {
                    'matches': matches,
                    'total': total,
                    'accuracy': accuracy
                }
            
            custom_results[region_key] = region_results
        
        return custom_results
    
    def _unity_python_random(self, seed: int) -> int:
        """模拟Unity Random.Range(1, 9)"""
        random.seed(seed)
        return random.randint(1, 8)
    
    def _hash_based_random(self, seed: int) -> int:
        """基于哈希的随机数生成"""
        hash_val = hash(seed) % 8
        return abs(hash_val) + 1
    
    def _linear_congruential(self, seed: int) -> int:
        """线性同余生成器"""
        a, c, m = 1664525, 1013904223, 2**32
        result = (a * seed + c) % m
        return (result % 8) + 1
    
    def _mersenne_twister_like(self, seed: int) -> int:
        """类似Mersenne Twister的算法"""
        # 简化版本
        seed = seed ^ (seed >> 11)
        seed = seed ^ ((seed << 7) & 0x9d2c5680)
        seed = seed ^ ((seed << 15) & 0xefc60000)
        seed = seed ^ (seed >> 18)
        return (abs(seed) % 8) + 1
    
    def generate_reverse_engineering_report(self, algorithm_patterns: Dict, custom_results: Dict) -> str:
        """
        生成逆向工程报告
        
        Args:
            algorithm_patterns: 算法模式分析结果
            custom_results: 自定义算法测试结果
            
        Returns:
            格式化的报告字符串
        """
        report = []
        report.append("=" * 80)
        report.append("🔬 种子算法逆向工程报告")
        report.append("=" * 80)
        report.append(f"📊 分析数据: {len(self.lottery_data)} 条开奖记录")
        report.append(f"🎯 分析区域: {len(self.perfect_regions)} 个100%准确率区域")
        report.append("")
        
        # 分析每个区域
        for region_key in algorithm_patterns.keys():
            report.append(f"📍 区域 {region_key} 分析结果:")
            report.append("-" * 60)
            
            # Unity Random测试结果
            unity_test = algorithm_patterns[region_key]['unity_random_test']
            if unity_test:
                report.append(f"🎲 Unity Random测试:")
                report.append(f"   匹配数: {unity_test['matches']}/{unity_test['total']}")
                report.append(f"   准确率: {unity_test['accuracy']:.2f}%")
                report.append("")
            
            # 取模模式测试
            modulo_patterns = algorithm_patterns[region_key]['modulo_patterns']
            if modulo_patterns:
                report.append("📐 取模模式测试:")
                for modulo, result in modulo_patterns.items():
                    report.append(f"   模{modulo}: {result['accuracy']:.2f}% ({result['matches']}/{result['total']})")
                report.append("")
            
            # 自定义算法测试
            custom_region = custom_results[region_key]
            report.append("🧪 自定义算法测试:")
            sorted_algos = sorted(custom_region.items(), key=lambda x: x[1]['accuracy'], reverse=True)
            for algo_name, result in sorted_algos:
                report.append(f"   {algo_name}: {result['accuracy']:.2f}% ({result['matches']}/{result['total']})")
            report.append("")
        
        # 总结最佳算法
        report.append("🏆 最佳算法候选:")
        report.append("-" * 60)
        
        best_algorithms = {}
        for region_key, custom_region in custom_results.items():
            best_algo = max(custom_region.items(), key=lambda x: x[1]['accuracy'])
            best_algorithms[region_key] = best_algo
            report.append(f"区域 {region_key}: {best_algo[0]} (准确率: {best_algo[1]['accuracy']:.2f}%)")
        
        report.append("")
        report.append("💡 结论和建议:")
        report.append("-" * 60)
        
        # 分析结论
        all_accuracies = []
        for region_data in custom_results.values():
            for algo_data in region_data.values():
                all_accuracies.append(algo_data['accuracy'])
        
        if all_accuracies:
            max_accuracy = max(all_accuracies)
            if max_accuracy >= 95:
                report.append("✅ 发现高度匹配的算法模式！")
            elif max_accuracy >= 80:
                report.append("⚠️ 发现部分匹配的算法模式，需要进一步优化")
            else:
                report.append("❌ 未发现明显的算法模式，可能需要更复杂的分析")
        
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def save_detailed_analysis(self, filename: str = "seed_reverse_analysis.json"):
        """
        保存详细分析结果到JSON文件
        
        Args:
            filename: 保存的文件名
        """
        analysis_data = {
            'perfect_regions': self.perfect_regions,
            'total_records': len(self.lottery_data),
            'seed_analysis_results': self.seed_analysis_results,
            'timestamp': str(datetime.now())
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(analysis_data, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"💾 详细分析结果已保存到: {filename}")

def main():
    """主函数"""
    print("🔬 种子算法逆向工程工具")
    print("=" * 50)
    
    # 创建逆向工程器
    engineer = SeedReverseEngineer()
    
    # 加载数据
    if not engineer.load_lottery_data('real_time_data_20250817.csv'):
        return
    
    # 分析100%准确率区域
    print("\n🎯 开始分析100%准确率区域...")
    engineer.analyze_perfect_regions()
    
    # 寻找算法模式
    print("\n🔍 寻找种子算法模式...")
    algorithm_patterns = engineer.find_seed_algorithm_patterns()
    
    # 测试自定义算法
    print("\n🧪 测试自定义算法...")
    custom_results = engineer.test_custom_algorithms()
    
    # 生成报告
    print("\n📋 生成逆向工程报告...")
    report = engineer.generate_reverse_engineering_report(algorithm_patterns, custom_results)
    print(report)
    
    # 保存报告
    with open('seed_reverse_engineering_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    # 保存详细分析
    engineer.save_detailed_analysis()
    
    print("\n✅ 逆向工程分析完成！")
    print("📄 报告已保存到: seed_reverse_engineering_report.txt")

if __name__ == "__main__":
    from datetime import datetime
    main()
