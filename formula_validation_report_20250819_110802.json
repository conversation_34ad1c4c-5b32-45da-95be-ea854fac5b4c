{"report": "================================================================================\n🔬 真实种子公式验证报告\n================================================================================\n📊 验证数据概览:\n  • 测试记录数: 230\n  • 验证公式数: 5\n\n🏆 公式准确率排名:\n  1. 主要公式 (偏移: -616)\n     准确率: 21.30% (49/230)\n\n  2. 备选公式1 (偏移: -934)\n     准确率: 20.43% (47/230)\n\n  3. 备选公式2 (偏移: 867)\n     准确率: 19.57% (45/230)\n\n  4. 备选公式3 (偏移: 895)\n     准确率: 19.13% (44/230)\n\n  5. 备选公式4 (偏移: -358)\n     准确率: 19.13% (44/230)\n\n🎯 最佳公式分析:\n  • 公式: 开奖时间戳 + -616\n  • 实际准确率: 21.30%\n  • 预期准确率: 86.00%\n  • 性能: 低于预期 64.70% ⚠️\n\n📈 预测分布分析:\n  公式 -616:\n    预测分布: 1:21, 2:31, 3:34, 4:30, 5:26, 6:29, 7:35, 8:24\n    实际分布: 1:27, 2:41, 3:24, 4:23, 5:28, 6:25, 7:32, 8:30\n\n  公式 -934:\n    预测分布: 1:33, 2:27, 3:31, 4:21, 5:25, 6:37, 7:29, 8:27\n    实际分布: 1:27, 2:41, 3:24, 4:23, 5:28, 6:25, 7:32, 8:30\n\n  公式 867:\n    预测分布: 1:25, 2:28, 3:22, 4:31, 5:27, 6:32, 7:38, 8:27\n    实际分布: 1:27, 2:41, 3:24, 4:23, 5:28, 6:25, 7:32, 8:30\n\n  公式 895:\n    预测分布: 1:38, 2:27, 3:28, 4:26, 5:30, 6:19, 7:34, 8:28\n    实际分布: 1:27, 2:41, 3:24, 4:23, 5:28, 6:25, 7:32, 8:30\n\n  公式 -358:\n    预测分布: 1:32, 2:32, 3:28, 4:35, 5:29, 6:28, 7:22, 8:24\n    实际分布: 1:27, 2:41, 3:24, 4:23, 5:28, 6:25, 7:32, 8:30\n\n❌ 错误模式分析:\n  公式 -616 常见错误:\n    预测6→实际8: 7次\n    预测7→实际2: 7次\n    预测3→实际2: 7次\n    预测3→实际5: 6次\n    预测3→实际8: 6次\n\n  公式 -934 常见错误:\n    预测1→实际7: 8次\n    预测6→实际5: 8次\n    预测6→实际1: 7次\n    预测8→实际2: 6次\n    预测3→实际8: 6次\n\n  公式 867 常见错误:\n    预测7→实际8: 8次\n    预测6→实际1: 7次\n    预测7→实际2: 7次\n    预测6→实际5: 7次\n    预测8→实际5: 6次\n\n  公式 895 常见错误:\n    预测1→实际8: 8次\n    预测4→实际2: 7次\n    预测5→实际2: 7次\n    预测7→实际2: 6次\n    预测2→实际4: 6次\n\n  公式 -358 常见错误:\n    预测4→实际2: 8次\n    预测6→实际2: 7次\n    预测5→实际2: 6次\n    预测4→实际5: 6次\n    预测3→实际7: 6次\n\n💡 结论和建议:\n  ❌ 公式验证失败，准确率过低: 21.30%\n  🔄 建议重新分析数据或检查算法\n================================================================================", "analysis": {"best_formula": {"offset": -616, "name": "主要公式", "accuracy": 0.21304347826086956, "expected_accuracy": 0.86}, "accuracy_ranking": [{"offset": -616, "name": "主要公式", "accuracy": 0.21304347826086956, "correct": 49, "total": 230}, {"offset": -934, "name": "备选公式1", "accuracy": 0.20434782608695654, "correct": 47, "total": 230}, {"offset": 867, "name": "备选公式2", "accuracy": 0.1956521739130435, "correct": 45, "total": 230}, {"offset": 895, "name": "备选公式3", "accuracy": 0.19130434782608696, "correct": 44, "total": 230}, {"offset": -358, "name": "备选公式4", "accuracy": 0.19130434782608696, "correct": 44, "total": 230}], "prediction_distribution": {"-616": {"predicted_counts": {"7": 35, "4": 30, "5": 26, "3": 34, "6": 29, "2": 31, "1": 21, "8": 24}, "actual_counts": {"7": 32, "4": 23, "2": 41, "8": 30, "5": 28, "1": 27, "3": 24, "6": 25}}, "-934": {"predicted_counts": {"1": 33, "3": 31, "5": 25, "8": 27, "2": 27, "4": 21, "6": 37, "7": 29}, "actual_counts": {"7": 32, "4": 23, "2": 41, "8": 30, "5": 28, "1": 27, "3": 24, "6": 25}}, "867": {"predicted_counts": {"8": 27, "3": 22, "7": 38, "2": 28, "1": 25, "5": 27, "4": 31, "6": 32}, "actual_counts": {"7": 32, "4": 23, "2": 41, "8": 30, "5": 28, "1": 27, "3": 24, "6": 25}}, "895": {"predicted_counts": {"2": 27, "4": 26, "1": 38, "7": 34, "3": 28, "6": 19, "5": 30, "8": 28}, "actual_counts": {"7": 32, "4": 23, "2": 41, "8": 30, "5": 28, "1": 27, "3": 24, "6": 25}}, "-358": {"predicted_counts": {"2": 32, "5": 29, "8": 24, "1": 32, "6": 28, "4": 35, "3": 28, "7": 22}, "actual_counts": {"7": 32, "4": 23, "2": 41, "8": 30, "5": 28, "1": 27, "3": 24, "6": 25}}}, "error_analysis": {"-616": {"error_count": 181, "common_errors": [[[6, 8], 7], [[7, 2], 7], [[3, 2], 7], [[3, 5], 6], [[3, 8], 6]]}, "-934": {"error_count": 183, "common_errors": [[[1, 7], 8], [[6, 5], 8], [[6, 1], 7], [[8, 2], 6], [[3, 8], 6]]}, "867": {"error_count": 185, "common_errors": [[[7, 8], 8], [[6, 1], 7], [[7, 2], 7], [[6, 5], 7], [[8, 5], 6]]}, "895": {"error_count": 186, "common_errors": [[[1, 8], 8], [[4, 2], 7], [[5, 2], 7], [[7, 2], 6], [[2, 4], 6]]}, "-358": {"error_count": 186, "common_errors": [[[4, 2], 8], [[6, 2], 7], [[5, 2], 6], [[4, 5], 6], [[3, 7], 6]]}}, "consistency_check": {}}, "summary": {"best_formula": {"offset": -616, "name": "主要公式", "accuracy": 0.21304347826086956, "expected_accuracy": 0.86}, "accuracy_ranking": [{"offset": -616, "name": "主要公式", "accuracy": 0.21304347826086956, "correct": 49, "total": 230}, {"offset": -934, "name": "备选公式1", "accuracy": 0.20434782608695654, "correct": 47, "total": 230}, {"offset": 867, "name": "备选公式2", "accuracy": 0.1956521739130435, "correct": 45, "total": 230}, {"offset": 895, "name": "备选公式3", "accuracy": 0.19130434782608696, "correct": 44, "total": 230}, {"offset": -358, "name": "备选公式4", "accuracy": 0.19130434782608696, "correct": 44, "total": 230}]}}