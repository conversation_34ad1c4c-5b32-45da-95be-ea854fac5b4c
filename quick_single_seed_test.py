#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速单种子测试
快速测试单种子的准确率
"""

import csv
import re
import random
from collections import Counter

def parse_csv_line(line: str):
    """解析CSV行"""
    fields = []
    current_field = ""
    in_quotes = False
    bracket_count = 0
    
    i = 0
    while i < len(line):
        char = line[i]
        
        if char == '"' and (i == 0 or line[i-1] != '\\'):
            in_quotes = not in_quotes
            current_field += char
        elif char == '[':
            bracket_count += 1
            current_field += char
        elif char == ']':
            bracket_count -= 1
            current_field += char
        elif char == ',' and not in_quotes and bracket_count == 0:
            fields.append(current_field.strip())
            current_field = ""
        else:
            current_field += char
        
        i += 1
    
    if current_field:
        fields.append(current_field.strip())
    
    return fields

def generate_random_sequence(draw_timestamp_seconds: int):
    """生成随机数序列"""
    first_seed = (draw_timestamp_seconds * 1000) - 5000
    
    random_sequence = []
    for i in range(9000):
        seed = first_seed + i
        random.seed(seed)
        result = random.randint(1, 8)
        random_sequence.append(result)
    
    return random_sequence

def test_position_accuracy(position: int, records):
    """测试单个位置的准确率"""
    successful = 0
    total = len(records)
    
    for record in records:
        random_sequence = generate_random_sequence(record['draw_timestamp_seconds'])
        predicted = random_sequence[position]
        actual = record['actual_output_room']
        
        if predicted == actual:
            successful += 1
    
    accuracy = (successful / total * 100) if total > 0 else 0
    return accuracy, successful, total

def main():
    """主函数"""
    print("🎯 快速单种子准确率测试")
    print("=" * 40)
    
    # 加载数据
    csv.field_size_limit(2000000)
    
    records = []
    with open('real_time_data_20250817.csv', 'r', encoding='utf-8') as f:
        content = f.read()
        lines = content.strip().split('\n')
        
        for i, line in enumerate(lines[1:], 1):
            try:
                fields = parse_csv_line(line)
                
                if len(fields) >= 7:
                    period = fields[1]
                    draw_timestamp = int(fields[2])
                    output_room = int(fields[6])
                    
                    records.append({
                        'period': period,
                        'draw_timestamp_seconds': draw_timestamp,
                        'actual_output_room': output_room
                    })
                    
            except Exception as e:
                continue
    
    print(f"✅ 加载了 {len(records)} 条记录")
    print()
    
    # 测试多个位置
    test_positions = [0, 500, 1000, 1500, 2000, 2276, 3000, 4000, 4362, 5000, 6000, 7000, 8000]
    
    print("📊 单种子位置准确率测试结果:")
    print("-" * 50)
    
    results = []
    
    for position in test_positions:
        accuracy, successful, total = test_position_accuracy(position, records)
        results.append((position, accuracy, successful, total))
        
        print(f"位置 {position:4d}: {accuracy:5.1f}% ({successful:3d}/{total})")
    
    print()
    
    # 统计分析
    accuracies = [result[1] for result in results]
    avg_accuracy = sum(accuracies) / len(accuracies)
    max_accuracy = max(accuracies)
    min_accuracy = min(accuracies)
    
    print("📈 统计分析:")
    print("-" * 30)
    print(f"平均准确率: {avg_accuracy:.1f}%")
    print(f"最高准确率: {max_accuracy:.1f}%")
    print(f"最低准确率: {min_accuracy:.1f}%")
    print(f"理论随机: {100/8:.1f}%")
    print()
    
    # 找出最佳位置
    best_result = max(results, key=lambda x: x[1])
    worst_result = min(results, key=lambda x: x[1])
    
    print("🏆 最佳和最差位置:")
    print("-" * 30)
    print(f"最佳位置: {best_result[0]} (准确率: {best_result[1]:.1f}%)")
    print(f"最差位置: {worst_result[0]} (准确率: {worst_result[1]:.1f}%)")
    print()
    
    # 与理论对比
    theoretical = 100 / 8
    print("🧮 与理论随机对比:")
    print("-" * 30)
    print(f"理论随机准确率: {theoretical:.1f}%")
    print(f"实际平均准确率: {avg_accuracy:.1f}%")
    print(f"差异: {avg_accuracy - theoretical:+.1f}%")
    
    if avg_accuracy > theoretical * 1.2:
        print("✅ 单种子预测显著优于随机!")
    elif avg_accuracy > theoretical * 1.05:
        print("⚠️ 单种子预测略优于随机")
    else:
        print("❌ 单种子预测接近随机水平")
    
    print()
    
    # 分析最佳位置的预测分布
    best_position = best_result[0]
    print(f"🔍 最佳位置 {best_position} 的预测分布:")
    print("-" * 40)
    
    predictions = []
    for record in records:
        random_sequence = generate_random_sequence(record['draw_timestamp_seconds'])
        predictions.append(random_sequence[best_position])
    
    distribution = Counter(predictions)
    total_predictions = len(predictions)
    
    print("预测分布:")
    for number in range(1, 9):
        count = distribution.get(number, 0)
        percentage = count / total_predictions * 100
        print(f"   房间{number}: {count:3d}次 ({percentage:5.1f}%)")
    
    most_common = distribution.most_common(1)[0]
    print(f"\n最常预测: 房间{most_common[0]} ({most_common[1]}次, {most_common[1]/total_predictions*100:.1f}%)")
    
    # 检查是否有明显偏好
    max_percentage = most_common[1] / total_predictions * 100
    if max_percentage > 20:
        print(f"⚠️ 该位置明显偏好房间{most_common[0]}")
    else:
        print("✅ 该位置预测分布相对均匀")

if __name__ == "__main__":
    main()
