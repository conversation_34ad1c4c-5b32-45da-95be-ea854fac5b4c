#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
区域优化分析工具
分析100%准确率区域是否过大，寻找最优化的区域配置
"""

import csv
import re
import random
from collections import Counter, defaultdict
from typing import List, Dict, Tuple, Set

class RegionOptimizer:
    """区域优化分析器"""
    
    def __init__(self):
        # 当前的100%准确率区域
        self.current_regions = [
            (2258, 2295, "区域A"),  # 38个种子
            (2259, 2296, "区域B"),  # 38个种子
            (4344, 4381, "区域C"),  # 38个种子
            (4345, 4382, "区域D"),  # 38个种子
            (4346, 4383, "区域E"),  # 38个种子
        ]
        
        self.lottery_data = []
    
    def load_lottery_data(self, csv_file: str) -> bool:
        """加载开奖数据"""
        try:
            csv.field_size_limit(2000000)
            
            with open(csv_file, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.strip().split('\n')
                
                for i, line in enumerate(lines[1:], 1):
                    try:
                        fields = self._parse_csv_line(line)
                        
                        if len(fields) >= 7:
                            period = fields[1]
                            draw_timestamp = int(fields[2])
                            output_room = int(fields[6])
                            
                            record = {
                                'period': period,
                                'draw_timestamp_seconds': draw_timestamp,
                                'actual_output_room': output_room
                            }
                            
                            self.lottery_data.append(record)
                        
                    except Exception as e:
                        continue
            
            print(f"✅ 成功加载 {len(self.lottery_data)} 条开奖记录")
            return len(self.lottery_data) > 0
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return False
    
    def _parse_csv_line(self, line: str) -> List[str]:
        """解析CSV行"""
        fields = []
        current_field = ""
        in_quotes = False
        bracket_count = 0
        
        i = 0
        while i < len(line):
            char = line[i]
            
            if char == '"' and (i == 0 or line[i-1] != '\\'):
                in_quotes = not in_quotes
                current_field += char
            elif char == '[':
                bracket_count += 1
                current_field += char
            elif char == ']':
                bracket_count -= 1
                current_field += char
            elif char == ',' and not in_quotes and bracket_count == 0:
                fields.append(current_field.strip())
                current_field = ""
            else:
                current_field += char
            
            i += 1
        
        if current_field:
            fields.append(current_field.strip())
        
        return fields
    
    def generate_random_sequence(self, draw_timestamp_seconds: int) -> List[int]:
        """生成随机数序列"""
        # 计算首个种子
        first_seed = (draw_timestamp_seconds * 1000) - 5000
        
        # 生成9000个随机数
        random_sequence = []
        for i in range(9000):
            seed = first_seed + i
            random.seed(seed)
            result = random.randint(1, 8)
            random_sequence.append(result)
        
        return random_sequence
    
    def analyze_region_coverage(self, start: int, end: int, region_name: str = "") -> Dict:
        """分析指定区域的覆盖情况"""
        region_size = end - start + 1
        
        coverage_stats = {
            'region_name': region_name,
            'start': start,
            'end': end,
            'size': region_size,
            'total_records': len(self.lottery_data),
            'successful_predictions': 0,
            'room_coverage': {i: 0 for i in range(1, 9)},  # 各房间被覆盖的次数
            'unique_room_sets': set(),  # 每次预测包含的房间集合
            'min_rooms_covered': 8,  # 最少覆盖房间数
            'max_rooms_covered': 0,  # 最多覆盖房间数
            'always_covered_rooms': set(range(1, 9)),  # 总是被覆盖的房间
            'never_covered_rooms': set(),  # 从不被覆盖的房间
        }
        
        for record in self.lottery_data:
            # 生成随机数序列
            random_sequence = self.generate_random_sequence(record['draw_timestamp_seconds'])
            
            # 提取指定区域的预测
            region_predictions = random_sequence[start:end+1]
            unique_rooms = set(region_predictions)
            
            # 检查是否包含实际房间
            actual_room = record['actual_output_room']
            if actual_room in region_predictions:
                coverage_stats['successful_predictions'] += 1
            
            # 统计房间覆盖
            for room in unique_rooms:
                coverage_stats['room_coverage'][room] += 1
            
            # 记录房间集合
            coverage_stats['unique_room_sets'].add(frozenset(unique_rooms))
            
            # 更新覆盖范围统计
            rooms_count = len(unique_rooms)
            coverage_stats['min_rooms_covered'] = min(coverage_stats['min_rooms_covered'], rooms_count)
            coverage_stats['max_rooms_covered'] = max(coverage_stats['max_rooms_covered'], rooms_count)
            
            # 更新总是覆盖的房间
            coverage_stats['always_covered_rooms'] &= unique_rooms
        
        # 计算从不被覆盖的房间
        all_covered_rooms = set()
        for room_set in coverage_stats['unique_room_sets']:
            all_covered_rooms |= room_set
        coverage_stats['never_covered_rooms'] = set(range(1, 9)) - all_covered_rooms
        
        # 计算准确率
        coverage_stats['accuracy_rate'] = (coverage_stats['successful_predictions'] / 
                                         coverage_stats['total_records'] * 100) if coverage_stats['total_records'] > 0 else 0
        
        return coverage_stats
    
    def find_optimal_regions(self) -> Dict:
        """寻找最优区域配置"""
        print("\n🔍 寻找最优区域配置...")
        
        optimization_results = {
            'current_regions_analysis': {},
            'size_optimization': {},
            'position_optimization': {},
            'recommendations': []
        }
        
        # 1. 分析当前区域
        print("📊 分析当前100%准确率区域...")
        for start, end, name in self.current_regions:
            analysis = self.analyze_region_coverage(start, end, name)
            optimization_results['current_regions_analysis'][name] = analysis
            
            print(f"   {name} ({start}-{end}): 准确率{analysis['accuracy_rate']:.1f}%, "
                  f"覆盖房间{analysis['min_rooms_covered']}-{analysis['max_rooms_covered']}个")
        
        # 2. 尝试缩小区域大小
        print("\n🔧 尝试优化区域大小...")
        size_tests = [10, 15, 20, 25, 30, 35]  # 测试不同的区域大小
        
        for size in size_tests:
            size_results = {}
            
            for start, end, name in self.current_regions:
                # 保持中心位置，缩小区域
                center = (start + end) // 2
                new_start = center - size // 2
                new_end = new_start + size - 1
                
                # 确保不超出原始范围
                new_start = max(new_start, 0)
                new_end = min(new_end, 8999)
                
                analysis = self.analyze_region_coverage(new_start, new_end, f"{name}_size{size}")
                size_results[name] = analysis
            
            optimization_results['size_optimization'][size] = size_results
            
            # 检查是否所有区域都保持100%准确率
            all_perfect = all(result['accuracy_rate'] == 100.0 for result in size_results.values())
            if all_perfect:
                print(f"   ✅ 区域大小{size}: 所有区域保持100%准确率")
            else:
                print(f"   ❌ 区域大小{size}: 部分区域准确率下降")
        
        # 3. 寻找最小有效区域
        print("\n🎯 寻找最小有效区域...")
        min_effective_sizes = {}
        
        for start, end, name in self.current_regions:
            center = (start + end) // 2
            
            # 从小到大测试区域大小
            for test_size in range(1, 39):
                test_start = center - test_size // 2
                test_end = test_start + test_size - 1
                
                test_start = max(test_start, 0)
                test_end = min(test_end, 8999)
                
                analysis = self.analyze_region_coverage(test_start, test_end)
                
                if analysis['accuracy_rate'] == 100.0:
                    min_effective_sizes[name] = {
                        'min_size': test_size,
                        'start': test_start,
                        'end': test_end,
                        'analysis': analysis
                    }
                    print(f"   {name}: 最小有效大小 {test_size} ({test_start}-{test_end})")
                    break
        
        optimization_results['min_effective_sizes'] = min_effective_sizes
        
        return optimization_results
    
    def analyze_room_distribution(self) -> Dict:
        """分析房间分布模式"""
        print("\n📈 分析房间分布模式...")
        
        distribution_analysis = {}
        
        for start, end, name in self.current_regions:
            room_distributions = []
            
            for record in self.lottery_data:
                random_sequence = self.generate_random_sequence(record['draw_timestamp_seconds'])
                region_predictions = random_sequence[start:end+1]
                
                # 统计各房间出现次数
                room_counts = Counter(region_predictions)
                room_distributions.append(room_counts)
            
            # 分析分布特征
            all_rooms_always_present = True
            room_presence = {i: 0 for i in range(1, 9)}
            
            for distribution in room_distributions:
                for room in range(1, 9):
                    if room in distribution:
                        room_presence[room] += 1
                    else:
                        all_rooms_always_present = False
            
            total_records = len(room_distributions)
            room_presence_rates = {room: count/total_records*100 
                                 for room, count in room_presence.items()}
            
            distribution_analysis[name] = {
                'all_rooms_always_present': all_rooms_always_present,
                'room_presence_rates': room_presence_rates,
                'min_presence_rate': min(room_presence_rates.values()),
                'max_presence_rate': max(room_presence_rates.values())
            }
            
            print(f"   {name}: 所有房间总是出现={all_rooms_always_present}, "
                  f"房间出现率范围{min(room_presence_rates.values()):.1f}%-{max(room_presence_rates.values()):.1f}%")
        
        return distribution_analysis
    
    def generate_optimization_report(self, optimization_results: Dict, distribution_analysis: Dict) -> str:
        """生成优化报告"""
        report = []
        report.append("🔧 区域优化分析报告")
        report.append("=" * 70)
        
        # 当前区域分析
        report.append("📊 当前区域分析:")
        for name, analysis in optimization_results['current_regions_analysis'].items():
            report.append(f"   {name} ({analysis['start']}-{analysis['end']}):")
            report.append(f"     区域大小: {analysis['size']}")
            report.append(f"     准确率: {analysis['accuracy_rate']:.1f}%")
            report.append(f"     覆盖房间数: {analysis['min_rooms_covered']}-{analysis['max_rooms_covered']}")
            report.append(f"     总是覆盖的房间: {sorted(list(analysis['always_covered_rooms']))}")
            report.append(f"     从不覆盖的房间: {sorted(list(analysis['never_covered_rooms']))}")
        report.append("")
        
        # 最小有效区域
        if 'min_effective_sizes' in optimization_results:
            report.append("🎯 最小有效区域:")
            for name, data in optimization_results['min_effective_sizes'].items():
                original_size = None
                for start, end, orig_name in self.current_regions:
                    if orig_name == name:
                        original_size = end - start + 1
                        break
                
                reduction = ((original_size - data['min_size']) / original_size * 100) if original_size else 0
                report.append(f"   {name}: {data['min_size']} ({data['start']}-{data['end']}) "
                             f"[减少{reduction:.1f}%]")
            report.append("")
        
        # 房间分布分析
        report.append("📈 房间分布分析:")
        for name, analysis in distribution_analysis.items():
            if analysis['all_rooms_always_present']:
                report.append(f"   {name}: ⚠️ 所有房间(1-8)总是出现 - 区域可能过大")
            else:
                report.append(f"   {name}: ✅ 房间出现有选择性")
                report.append(f"     房间出现率: {analysis['min_presence_rate']:.1f}%-{analysis['max_presence_rate']:.1f}%")
        report.append("")
        
        # 优化建议
        report.append("💡 优化建议:")
        
        # 检查是否所有区域都包含全部房间
        all_regions_full_coverage = all(
            analysis['all_rooms_always_present'] 
            for analysis in distribution_analysis.values()
        )
        
        if all_regions_full_coverage:
            report.append("   ⚠️ 问题确认: 所有区域都包含全部房间1-8")
            report.append("   📉 这解释了为什么准确率是100% - 区域过大导致完全覆盖")
            report.append("")
            report.append("   🔧 建议的优化方案:")
            
            if 'min_effective_sizes' in optimization_results:
                report.append("   1. 使用最小有效区域:")
                for name, data in optimization_results['min_effective_sizes'].items():
                    report.append(f"      {name}: 大小{data['min_size']} ({data['start']}-{data['end']})")
            
            report.append("   2. 寻找更精确的预测区域:")
            report.append("      - 测试更小的区域大小(5-15)")
            report.append("      - 寻找房间分布有差异的区域")
            report.append("      - 考虑使用单一最优区域而非多区域")
            
        else:
            report.append("   ✅ 区域配置合理，房间覆盖有选择性")
        
        report.append("=" * 70)
        
        return "\n".join(report)

def main():
    """主函数"""
    print("🔧 区域优化分析工具")
    print("=" * 60)
    print("分析100%准确率区域是否过大，寻找最优配置")
    print()
    
    # 创建优化器
    optimizer = RegionOptimizer()
    
    # 加载数据
    if not optimizer.load_lottery_data('real_time_data_20250817.csv'):
        return
    
    # 寻找最优区域
    optimization_results = optimizer.find_optimal_regions()
    
    # 分析房间分布
    distribution_analysis = optimizer.analyze_room_distribution()
    
    # 生成报告
    report = optimizer.generate_optimization_report(optimization_results, distribution_analysis)
    print(report)
    
    # 保存报告
    with open('region_optimization_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n💾 优化分析报告已保存到: region_optimization_report.txt")

if __name__ == "__main__":
    main()
