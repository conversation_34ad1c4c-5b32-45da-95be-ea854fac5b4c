#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于逆向分析的预测器
利用逆向种子分析的结果进行更精确的预测
"""

import random
import time
from datetime import datetime
from collections import Counter, defaultdict
from typing import List, Dict, Set

class ReverseBasedPredictor:
    """基于逆向分析的预测器"""
    
    def __init__(self):
        # 基于逆向分析发现的热门位置
        self.hot_positions = [3244, 4692, 6103, 78, 1145, 2352, 3156, 7583, 7739, 1150]
        
        # 各房间的偏好位置（基于逆向分析）
        self.room_preferred_positions = {
            1: [441, 4692, 78],
            2: [4322, 1145, 2352],
            3: [6545, 6103],
            4: [2142],
            5: [3597],
            6: [6317, 2097],
            7: [1227, 3156],
            8: [4382, 7583]
        }
        
        # 理论上每个房间约有1142个匹配位置
        self.expected_matches_per_room = 1142
    
    def calculate_first_seed(self, draw_timestamp_seconds: int) -> int:
        """计算首个种子"""
        return (draw_timestamp_seconds * 1000) - 5000
    
    def unity_random_algorithm(self, seed: int) -> int:
        """Unity Random算法"""
        random.seed(seed)
        return random.randint(1, 8)
    
    def find_all_matching_positions(self, draw_timestamp_seconds: int, target_room: int) -> List[int]:
        """找出产生指定房间的所有位置"""
        first_seed = self.calculate_first_seed(draw_timestamp_seconds)
        
        matching_positions = []
        
        for position in range(9000):
            seed = first_seed + position
            generated_room = self.unity_random_algorithm(seed)
            
            if generated_room == target_room:
                matching_positions.append(position)
        
        return matching_positions
    
    def analyze_position_distribution(self, draw_timestamp_seconds: int) -> Dict:
        """分析所有房间的位置分布"""
        room_positions = {}
        
        for room in range(1, 9):
            positions = self.find_all_matching_positions(draw_timestamp_seconds, room)
            room_positions[room] = positions
        
        return room_positions
    
    def predict_with_hot_positions(self, draw_timestamp_seconds: int) -> Dict:
        """使用热门位置进行预测"""
        first_seed = self.calculate_first_seed(draw_timestamp_seconds)
        
        hot_position_predictions = {}
        
        for position in self.hot_positions:
            seed = first_seed + position
            predicted_room = self.unity_random_algorithm(seed)
            
            hot_position_predictions[position] = predicted_room
        
        # 统计预测结果
        prediction_counts = Counter(hot_position_predictions.values())
        
        return {
            'strategy': 'hot_positions',
            'position_predictions': hot_position_predictions,
            'prediction_counts': dict(prediction_counts),
            'most_likely': prediction_counts.most_common(1)[0] if prediction_counts else (0, 0),
            'predicted_rooms': list(prediction_counts.keys())
        }
    
    def predict_with_room_preferences(self, draw_timestamp_seconds: int) -> Dict:
        """使用房间偏好位置进行预测"""
        first_seed = self.calculate_first_seed(draw_timestamp_seconds)
        
        room_preference_predictions = {}
        
        for room, preferred_positions in self.room_preferred_positions.items():
            room_predictions = []
            
            for position in preferred_positions:
                seed = first_seed + position
                predicted_room = self.unity_random_algorithm(seed)
                room_predictions.append(predicted_room)
            
            # 检查该房间的偏好位置是否预测了自己
            self_predictions = room_predictions.count(room)
            total_predictions = len(room_predictions)
            
            room_preference_predictions[room] = {
                'preferred_positions': preferred_positions,
                'predictions': room_predictions,
                'self_prediction_count': self_predictions,
                'self_prediction_rate': self_predictions / total_predictions * 100 if total_predictions > 0 else 0
            }
        
        return {
            'strategy': 'room_preferences',
            'room_predictions': room_preference_predictions
        }
    
    def predict_with_position_sampling(self, draw_timestamp_seconds: int, sample_size: int = 100) -> Dict:
        """使用位置采样进行预测"""
        first_seed = self.calculate_first_seed(draw_timestamp_seconds)
        
        # 均匀采样位置
        step = 9000 // sample_size
        sample_positions = list(range(0, 9000, step))[:sample_size]
        
        sample_predictions = {}
        
        for position in sample_positions:
            seed = first_seed + position
            predicted_room = self.unity_random_algorithm(seed)
            sample_predictions[position] = predicted_room
        
        # 统计结果
        prediction_counts = Counter(sample_predictions.values())
        
        return {
            'strategy': 'position_sampling',
            'sample_size': sample_size,
            'sample_positions': sample_positions,
            'sample_predictions': sample_predictions,
            'prediction_counts': dict(prediction_counts),
            'distribution_percentages': {room: count/sample_size*100 
                                       for room, count in prediction_counts.items()}
        }
    
    def comprehensive_prediction(self, draw_timestamp_seconds: int) -> Dict:
        """综合预测"""
        # 获取各种策略的预测
        hot_prediction = self.predict_with_hot_positions(draw_timestamp_seconds)
        room_preference = self.predict_with_room_preferences(draw_timestamp_seconds)
        sampling_prediction = self.predict_with_position_sampling(draw_timestamp_seconds)
        
        # 综合分析
        comprehensive_result = {
            'timestamp_seconds': draw_timestamp_seconds,
            'datetime': datetime.fromtimestamp(draw_timestamp_seconds).strftime('%Y-%m-%d %H:%M:%S'),
            'first_seed': self.calculate_first_seed(draw_timestamp_seconds),
            'hot_positions_prediction': hot_prediction,
            'room_preferences_prediction': room_preference,
            'sampling_prediction': sampling_prediction
        }
        
        # 计算综合建议
        all_predictions = []
        
        # 收集热门位置预测
        all_predictions.extend(hot_prediction['predicted_rooms'])
        
        # 收集采样预测
        all_predictions.extend(sampling_prediction['prediction_counts'].keys())
        
        # 统计综合结果
        comprehensive_counts = Counter(all_predictions)
        
        comprehensive_result['comprehensive_analysis'] = {
            'all_predictions': all_predictions,
            'comprehensive_counts': dict(comprehensive_counts),
            'most_likely_overall': comprehensive_counts.most_common(1)[0] if comprehensive_counts else (0, 0),
            'consensus_rooms': [room for room, count in comprehensive_counts.items() if count >= 2]
        }
        
        return comprehensive_result
    
    def format_prediction_report(self, prediction: Dict) -> str:
        """格式化预测报告"""
        report = []
        report.append("🔍 基于逆向分析的预测报告")
        report.append("=" * 60)
        report.append(f"⏰ 预测时间: {prediction['datetime']}")
        report.append(f"🔢 时间戳: {prediction['timestamp_seconds']}")
        report.append(f"🌱 首个种子: {prediction['first_seed']}")
        report.append("")
        
        # 热门位置预测
        hot_pred = prediction['hot_positions_prediction']
        report.append("🔥 热门位置预测:")
        report.append(f"   预测结果: {hot_pred['prediction_counts']}")
        if hot_pred['most_likely'][1] > 0:
            report.append(f"   最可能: 房间{hot_pred['most_likely'][0]} (出现{hot_pred['most_likely'][1]}次)")
        report.append("")
        
        # 采样预测
        sampling_pred = prediction['sampling_prediction']
        report.append("📊 位置采样预测 (100个位置):")
        sorted_sampling = sorted(sampling_pred['distribution_percentages'].items(), 
                               key=lambda x: x[1], reverse=True)
        for room, percentage in sorted_sampling:
            report.append(f"   房间{room}: {percentage:.1f}%")
        report.append("")
        
        # 房间偏好分析
        room_pref = prediction['room_preferences_prediction']
        report.append("🎲 房间偏好位置分析:")
        for room, data in room_pref['room_predictions'].items():
            if data['self_prediction_rate'] > 0:
                report.append(f"   房间{room}: {data['self_prediction_rate']:.1f}%的偏好位置预测自己")
        report.append("")
        
        # 综合建议
        comp_analysis = prediction['comprehensive_analysis']
        report.append("💡 综合预测建议:")
        
        if comp_analysis['consensus_rooms']:
            report.append(f"   🎯 多策略共识: 房间 {comp_analysis['consensus_rooms']}")
        
        most_likely = comp_analysis['most_likely_overall']
        if most_likely[1] > 0:
            report.append(f"   📈 综合最可能: 房间{most_likely[0]} (权重: {most_likely[1]})")
        
        # 理论分析
        report.append("")
        report.append("🧮 理论分析:")
        report.append("   - 每个房间理论上有约1142个匹配位置")
        report.append("   - 热门位置基于历史命中频率选择")
        report.append("   - 采样预测反映整体分布趋势")
        report.append("   - 房间偏好基于逆向分析的位置偏好")
        
        report.append("=" * 60)
        
        return "\n".join(report)
    
    def interactive_mode(self):
        """交互式预测模式"""
        print("🔍 基于逆向分析的预测器")
        print("=" * 50)
        print("利用逆向种子分析结果进行预测")
        print("")
        print("命令说明:")
        print("  1 - 当前时间综合预测")
        print("  2 - 自定义时间戳预测")
        print("  3 - 分析指定时间戳的位置分布")
        print("  4 - 热门位置预测")
        print("  q - 退出")
        print("=" * 50)
        
        while True:
            try:
                choice = input("\n请选择操作 (1-4, q): ").strip().lower()
                
                if choice == 'q':
                    print("👋 感谢使用基于逆向分析的预测器！")
                    break
                
                elif choice == '1':
                    current_timestamp = int(time.time())
                    prediction = self.comprehensive_prediction(current_timestamp)
                    report = self.format_prediction_report(prediction)
                    print(report)
                
                elif choice == '2':
                    timestamp_input = input("请输入时间戳（秒）: ").strip()
                    try:
                        timestamp = int(timestamp_input)
                        prediction = self.comprehensive_prediction(timestamp)
                        report = self.format_prediction_report(prediction)
                        print(report)
                    except ValueError:
                        print("❌ 时间戳格式错误")
                
                elif choice == '3':
                    timestamp_input = input("请输入时间戳（秒）: ").strip()
                    try:
                        timestamp = int(timestamp_input)
                        print("\n🔍 分析位置分布...")
                        
                        room_positions = self.analyze_position_distribution(timestamp)
                        
                        print("📊 各房间的匹配位置数量:")
                        for room in range(1, 9):
                            count = len(room_positions[room])
                            percentage = count / 9000 * 100
                            print(f"   房间{room}: {count}个位置 ({percentage:.1f}%)")
                        
                        print(f"\n理论期望: 每房间约{self.expected_matches_per_room}个位置 (12.5%)")
                        
                    except ValueError:
                        print("❌ 时间戳格式错误")
                
                elif choice == '4':
                    timestamp_input = input("请输入时间戳（秒）: ").strip()
                    try:
                        timestamp = int(timestamp_input)
                        hot_prediction = self.predict_with_hot_positions(timestamp)
                        
                        print("\n🔥 热门位置预测结果:")
                        print("-" * 30)
                        for position, room in hot_prediction['position_predictions'].items():
                            print(f"位置{position}: 房间{room}")
                        
                        print(f"\n📊 预测统计: {hot_prediction['prediction_counts']}")
                        if hot_prediction['most_likely'][1] > 0:
                            print(f"🎯 最可能: 房间{hot_prediction['most_likely'][0]}")
                        
                    except ValueError:
                        print("❌ 时间戳格式错误")
                
                else:
                    print("❌ 无效选择，请重新输入")
                    
            except KeyboardInterrupt:
                print("\n👋 用户中断，退出程序")
                break
            except Exception as e:
                print(f"❌ 发生错误: {e}")

def main():
    """主函数"""
    predictor = ReverseBasedPredictor()
    predictor.interactive_mode()

if __name__ == "__main__":
    main()
