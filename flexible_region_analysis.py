#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
索引区域准确率统计系统 - 滑动窗口模式

功能描述:
- 使用滑动窗口模式将索引位置划分成重叠区域
- 例如区域大小4：区域1[0,1,2,3], 区域2[1,2,3,4], 区域3[2,3,4,5]
- 统计每个区域的整体准确率和全覆盖率
- 比较不同区域大小的效果，推荐最佳区域大小
- 按准确率排序，识别表现最佳的索引区域

使用方法:
    python flexible_region_analysis.py

输出文件:
    region_size_5_accuracy_report.txt  - 推荐的区域大小(5)统计报告
    region_size_10_accuracy_report.txt - 区域大小10的统计报告
    region_size_15_accuracy_report.txt - 区域大小15的统计报告
    region_size_20_accuracy_report.txt - 区域大小20的统计报告
    region_size_25_accuracy_report.txt - 区域大小25的统计报告
    region_size_30_accuracy_report.txt - 区域大小30的统计报告

统计逻辑:
    区域大小 = N (可配置，推荐N=5)
    区域数量 = 9000 / N

    对于每条记录:
        命中区域集合 = 空集合
        对于预测列表中的每个索引位置i:
            区域ID = i // N
            如果 predict_numbers[i] == actual_number:
                命中区域集合.add(区域ID)

        对于每个区域ID:
            区域总记录数 += 1
            如果 区域ID in 命中区域集合:
                区域命中次数 += 1

    区域准确率 = 命中次数 / 总记录数 * 100%

推荐配置:
    最佳区域大小: 5个索引位置
    理由: 标准差最大(4.03%)，能更好地区分不同区域的表现

作者: 预测分析系统
版本: 2.0
"""

import csv
import ast
import sys
from collections import defaultdict
from config import *

# 增加CSV字段大小限制
csv.field_size_limit(CSV_FIELD_SIZE_LIMIT)

def analyze_region_accuracy_flexible(region_size=10):
    """按指定大小的索引区域分析预测准确率"""
    data_files = get_data_files()

    if not data_files:
        print("❌ 没有找到数据文件")
        return []

    print(f"=== 按索引区域统计预测准确率 ===")
    print(f"区域大小: {region_size}个索引位置")
    print(f"📊 处理 {len(data_files)} 个数据文件")

    # 统计每个区域的情况
    region_stats = defaultdict(lambda: {
        'total_count': 0,
        'hit_count': 0,
        'full_coverage_count': 0  # 包含1-8所有号码的行数
    })

    total_records = 0
    valid_records = 0
    processed_files = 0

    for file_idx, filename in enumerate(data_files):
        print(f"\n📄 处理文件 {file_idx + 1}/{len(data_files)}: {filename}")

        try:
            with open(filename, 'r', encoding=FILE_ENCODING) as file:
                reader = csv.reader(file)
                headers = next(reader)

                file_records = 0
                file_valid_records = 0

                for row_num, row in enumerate(reader):
                    total_records += 1
                    file_records += 1
            
                    try:
                        period = row[1]
                        timestamp = row[0]

                        # 提取预测号码序列
                        predict_numbers = []
                        if len(row) > 4 and row[4]:
                            predict_str = row[4].strip()
                            if predict_str and predict_str.startswith('[') and predict_str.endswith(']'):
                                try:
                                    predict_numbers = ast.literal_eval(predict_str)
                                except Exception as e:
                                    continue

                        # 提取开出的号码
                        actual_number = None
                        if len(row) > 6 and row[6]:
                            try:
                                actual_number = int(row[6])
                            except:
                                continue

                        # 只处理有效的记录
                        if predict_numbers and actual_number is not None:
                            valid_records += 1
                            file_valid_records += 1

                            # 分析每个区域的命中情况（滑动窗口模式）
                            region_hits = set()  # 记录命中的区域
                            region_full_coverage = set()  # 记录包含1-8所有号码的区域

                            # 滑动窗口分区：每个区域大小为region_size，步长为1
                            # 总区域数 = 预测序列长度 - 区域大小 + 1
                            total_regions = len(predict_numbers) - region_size + 1

                            if total_regions > 0:
                                for region_id in range(total_regions):
                                    start_idx = region_id  # 滑动窗口起始位置
                                    end_idx = start_idx + region_size
                                    region_numbers = predict_numbers[start_idx:end_idx]

                                    # 检查该区域是否包含1-8所有号码
                                    region_number_set = set(region_numbers)
                                    full_set = set(range(1, 9))  # {1, 2, 3, 4, 5, 6, 7, 8}
                                    if full_set.issubset(region_number_set):
                                        region_full_coverage.add(region_id)

                                    # 检查该区域是否命中
                                    if actual_number in region_numbers:
                                        region_hits.add(region_id)

                                # 统计每个区域的情况
                                for region_id in range(total_regions):
                                    region_stats[region_id]['total_count'] += 1
                                    if region_id in region_hits:
                                        region_stats[region_id]['hit_count'] += 1
                                    if region_id in region_full_coverage:
                                        region_stats[region_id]['full_coverage_count'] += 1

                    except Exception as e:
                        print(f"文件 {filename} 第{row_num + 1}行处理出错: {e}")

                print(f"  ✅ 文件处理完成: {file_records}条记录，{file_valid_records}条有效")
                processed_files += 1

        except Exception as e:
            print(f"❌ 文件 {filename} 处理失败: {e}")
            continue
    
    print(f"\n=== 基本统计 ===")
    print(f"处理文件数: {processed_files}/{len(data_files)}")
    print(f"总记录数: {total_records}")
    print(f"有效记录数: {valid_records}")
    print(f"总区域数: {len(region_stats)}")
    
    # 计算每个区域的准确率并排序
    region_accuracies = []
    for region_id in sorted(region_stats.keys()):
        total_count = region_stats[region_id]['total_count']
        hit_count = region_stats[region_id]['hit_count']
        full_coverage_count = region_stats[region_id]['full_coverage_count']
        accuracy = hit_count / total_count * 100 if total_count > 0 else 0
        full_coverage_rate = full_coverage_count / total_count * 100 if total_count > 0 else 0

        start_index = region_id  # 滑动窗口模式：起始索引就是区域ID
        end_index = start_index + region_size - 1

        region_accuracies.append({
            'region_id': region_id,
            'start_index': start_index,
            'end_index': end_index,
            'total_count': total_count,
            'hit_count': hit_count,
            'full_coverage_count': full_coverage_count,
            'accuracy': accuracy,
            'full_coverage_rate': full_coverage_rate
        })
    
    # 按准确率从高到低排序
    region_accuracies.sort(key=lambda x: x['accuracy'], reverse=True)
    
    # 显示排序后的结果
    print(f"\n=== 各区域准确率统计（按准确率排序）===")
    print("排名 | 区域 | 索引范围      | 总记录数 | 命中次数 | 准确率 | 全覆盖行数 | 全覆盖率")
    print("-" * 85)
    
    # 显示前N名
    for rank, item in enumerate(region_accuracies[:TOP_REGION_DISPLAY_COUNT], 1):
        region_range = f"{item['start_index']:4d}-{item['end_index']:4d}"
        print(f"{rank:3d}  | {item['region_id']:3d}  | {region_range} |   {item['total_count']:3d}    |   {item['hit_count']:3d}    | {item['accuracy']:6.2f}% |    {item['full_coverage_count']:3d}     | {item['full_coverage_rate']:6.2f}%")
    
    print(f"\n显示了前{TOP_REGION_DISPLAY_COUNT}名，总共有{len(region_accuracies)}个区域")
    
    # 显示一些统计信息
    print(f"\n=== 区域准确率统计信息 ===")
    accuracies = [item['accuracy'] for item in region_accuracies]
    full_coverage_rates = [item['full_coverage_rate'] for item in region_accuracies]
    print(f"最高准确率: {max(accuracies):.2f}%")
    print(f"最低准确率: {min(accuracies):.2f}%")
    print(f"平均准确率: {sum(accuracies)/len(accuracies):.2f}%")
    print(f"准确率标准差: {(sum([(acc - sum(accuracies)/len(accuracies))**2 for acc in accuracies]) / len(accuracies))**0.5:.2f}%")

    print(f"\n=== 全覆盖统计信息 ===")
    print(f"最高全覆盖率: {max(full_coverage_rates):.2f}%")
    print(f"最低全覆盖率: {min(full_coverage_rates):.2f}%")
    print(f"平均全覆盖率: {sum(full_coverage_rates)/len(full_coverage_rates):.2f}%")

    # 统计全覆盖率区间分布
    full_coverage_ranges = [
        (90, float('inf'), "90%以上"),
        (70, 90, "70%-90%"),
        (50, 70, "50%-70%"),
        (30, 50, "30%-50%"),
        (10, 30, "10%-30%"),
        (0, 10, "10%以下")
    ]

    print(f"\n=== 全覆盖率区间分布 ===")
    for min_rate, max_rate, label in full_coverage_ranges:
        count = sum(1 for rate in full_coverage_rates if min_rate <= rate < max_rate)
        percentage = count / len(full_coverage_rates) * 100
        print(f"{label}: {count}个区域 ({percentage:.1f}%)")
    
    # 统计不同准确率区间的区域数量
    print(f"\n=== 准确率区间分布 ===")
    ranges = REGION_ACCURACY_RANGES
    
    for min_acc, max_acc, label in ranges:
        count = sum(1 for acc in accuracies if min_acc <= acc < max_acc)
        percentage = count / len(accuracies) * 100
        print(f"{label}: {count}个区域 ({percentage:.1f}%)")
    
    # 保存详细报告
    save_flexible_report(region_accuracies, region_size, valid_records)
    
    return region_accuracies

def save_flexible_report(region_accuracies, region_size, valid_records):
    """保存灵活区域准确率分析报告"""
    report_filename = get_region_report_filename(region_size)
    
    with open(report_filename, 'w', encoding='utf-8') as f:
        f.write(f"=== 区域大小{region_size}的索引区域统计预测准确率报告 ===\n")
        f.write(f"生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"区域大小: {region_size}个索引位置\n")
        f.write(f"分析的有效记录数: {valid_records}\n\n")
        
        f.write("=== 各区域准确率统计（按准确率排序）===\n")
        f.write("排名 | 区域 | 索引范围      | 总记录数 | 命中次数 | 准确率 | 全覆盖行数 | 全覆盖率\n")
        f.write("-" * 85 + "\n")
        
        # 保存所有区域结果
        for rank, item in enumerate(region_accuracies, 1):
            region_range = f"{item['start_index']:4d}-{item['end_index']:4d}"
            f.write(f"{rank:3d}  | {item['region_id']:3d}  | {region_range} |   {item['total_count']:3d}    |   {item['hit_count']:3d}    | {item['accuracy']:6.2f}% |    {item['full_coverage_count']:3d}     | {item['full_coverage_rate']:6.2f}%\n")
        
        # 统计信息
        accuracies = [item['accuracy'] for item in region_accuracies]
        full_coverage_rates = [item['full_coverage_rate'] for item in region_accuracies]
        f.write(f"\n=== 统计摘要 ===\n")
        f.write(f"总区域数: {len(region_accuracies)}\n")
        f.write(f"最高准确率: {max(accuracies):.2f}% (区域{region_accuracies[0]['region_id']})\n")
        f.write(f"最低准确率: {min(accuracies):.2f}% (区域{region_accuracies[-1]['region_id']})\n")
        f.write(f"平均准确率: {sum(accuracies)/len(accuracies):.2f}%\n")
        f.write(f"准确率标准差: {(sum([(acc - sum(accuracies)/len(accuracies))**2 for acc in accuracies]) / len(accuracies))**0.5:.2f}%\n")

        f.write(f"\n=== 全覆盖统计摘要 ===\n")
        f.write(f"最高全覆盖率: {max(full_coverage_rates):.2f}%\n")
        f.write(f"最低全覆盖率: {min(full_coverage_rates):.2f}%\n")
        f.write(f"平均全覆盖率: {sum(full_coverage_rates)/len(full_coverage_rates):.2f}%\n")
        
        # 准确率区间分布
        f.write(f"\n=== 准确率区间分布 ===\n")
        ranges = REGION_ACCURACY_RANGES
        
        for min_acc, max_acc, label in ranges:
            count = sum(1 for acc in accuracies if min_acc <= acc < max_acc)
            percentage = count / len(accuracies) * 100
            f.write(f"{label}: {count}个区域 ({percentage:.1f}%)\n")
    
    print(f"详细报告已保存到: {report_filename}")

def compare_different_sizes():
    """比较不同区域大小的效果"""
    sizes_to_test = REGION_SIZES_TO_TEST
    
    print("=== 比较不同区域大小的效果 ===")
    print("区域大小 | 总区域数 | 最高准确率 | 最低准确率 | 平均准确率 | 标准差")
    print("-" * 70)
    
    results = []
    
    for size in sizes_to_test:
        region_accuracies = analyze_region_accuracy_flexible(size)
        accuracies = [item['accuracy'] for item in region_accuracies]
        
        max_acc = max(accuracies)
        min_acc = min(accuracies)
        avg_acc = sum(accuracies) / len(accuracies)
        std_dev = (sum([(acc - avg_acc)**2 for acc in accuracies]) / len(accuracies))**0.5
        
        results.append({
            'size': size,
            'total_regions': len(region_accuracies),
            'max_accuracy': max_acc,
            'min_accuracy': min_acc,
            'avg_accuracy': avg_acc,
            'std_dev': std_dev
        })
        
        print(f"   {size:2d}    |   {len(region_accuracies):4d}   |   {max_acc:6.2f}%   |   {min_acc:6.2f}%   |   {avg_acc:6.2f}%   | {std_dev:6.2f}%")
        print()  # 空行分隔
    
    # 找出最佳区域大小
    best_size = max(results, key=lambda x: x['std_dev'])
    print(f"=== 推荐区域大小 ===")
    print(f"建议使用区域大小: {best_size['size']}")
    print(f"理由: 标准差最大({best_size['std_dev']:.2f}%)，能更好地区分不同区域的表现")
    
    return results

if __name__ == "__main__":
    # 比较不同区域大小
    comparison_results = compare_different_sizes()
    
    print(f"\n=== 分析完成 ===")
    print(f"已生成多个不同区域大小的分析报告")
    print(f"请查看生成的报告文件选择最合适的区域大小")
