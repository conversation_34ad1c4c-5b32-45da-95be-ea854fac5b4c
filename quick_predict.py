#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速预测工具
简化版本，用于快速获取避开建议
"""

import time
import random
from datetime import datetime

def unity_random_next(seed: int) -> int:
    """模拟Unity Random.Range(1, 9)"""
    random.seed(seed)
    return random.randint(1, 8)

def quick_predict(timestamp_ms: int = None) -> dict:
    """
    快速预测函数
    
    Args:
        timestamp_ms: 时间戳（毫秒），如果为None则使用当前时间
        
    Returns:
        预测结果字典
    """
    if timestamp_ms is None:
        timestamp_ms = int(time.time() * 1000)
    
    # 基于统计数据的最优区域（准确率最高的前3个区域）
    top_regions = [
        (5994, 6003, 78.40),  # 区域5994-6003，准确率78.40%
        (6610, 6619, 77.86),  # 区域6610-6619，准确率77.86%
        (5993, 6002, 77.62),  # 区域5993-6002，准确率77.62%
    ]
    
    # 收集所有预测号码
    all_predictions = []
    region_info = []
    
    for start, end, accuracy in top_regions:
        predictions = []
        for offset in range(start, end + 1):
            seed = timestamp_ms + offset
            predicted_num = unity_random_next(seed)
            predictions.append(predicted_num)
        
        all_predictions.extend(predictions)
        region_info.append({
            'range': f"{start}-{end}",
            'accuracy': accuracy,
            'predictions': predictions
        })
    
    # 统计号码出现频次
    number_counts = {}
    for num in all_predictions:
        number_counts[num] = number_counts.get(num, 0) + 1
    
    # 找出出现频次最高的号码作为避开目标
    if number_counts:
        max_count = max(number_counts.values())
        avoid_numbers = [num for num, count in number_counts.items() 
                        if count >= max_count * 0.7]  # 出现频次达到最高频次70%以上
    else:
        avoid_numbers = []
    
    # 计算安全号码
    all_numbers = set(range(1, 9))
    predicted_numbers = set(all_predictions)
    safe_numbers = all_numbers - set(avoid_numbers)
    
    # 计算平均置信度
    avg_confidence = sum(accuracy for _, _, accuracy in top_regions) / len(top_regions)
    
    return {
        'timestamp': timestamp_ms,
        'datetime': datetime.fromtimestamp(timestamp_ms / 1000).strftime('%Y-%m-%d %H:%M:%S'),
        'regions_used': region_info,
        'avoid_numbers': sorted(avoid_numbers),
        'safe_numbers': sorted(list(safe_numbers)),
        'all_predicted': sorted(list(predicted_numbers)),
        'confidence': avg_confidence,
        'recommendation': sorted(list(safe_numbers)) if safe_numbers else "谨慎投注"
    }

def print_prediction(result: dict):
    """打印预测结果"""
    print("🎯 快速预测结果")
    print("=" * 40)
    print(f"⏰ 时间: {result['datetime']}")
    print(f"📊 置信度: {result['confidence']:.1f}%")
    print()
    
    print("📍 使用区域:")
    for region in result['regions_used']:
        print(f"  • {region['range']} (准确率: {region['accuracy']:.1f}%)")
    print()
    
    print(f"🚫 建议避开: {result['avoid_numbers']}")
    print(f"✅ 推荐选择: {result['safe_numbers']}")
    print()
    
    if isinstance(result['recommendation'], list) and result['recommendation']:
        print(f"💡 最终建议: 选择号码 {result['recommendation']}")
    else:
        print("💡 最终建议: 所有号码都有风险，建议谨慎投注")
    print("=" * 40)

def main():
    """主函数"""
    print("🎮 避开类游戏 - 快速预测工具")
    print()
    
    while True:
        try:
            choice = input("选择操作:\n1. 当前时间预测\n2. 自定义时间戳\n3. 退出\n请输入 (1-3): ").strip()
            
            if choice == '1':
                result = quick_predict()
                print_prediction(result)
                
            elif choice == '2':
                timestamp_input = input("请输入时间戳（毫秒级）: ").strip()
                try:
                    timestamp = int(timestamp_input)
                    result = quick_predict(timestamp)
                    print_prediction(result)
                except ValueError:
                    print("❌ 时间戳格式错误，请输入数字")
                    
            elif choice == '3':
                print("👋 再见！")
                break
                
            else:
                print("❌ 无效选择")
                
            print()
            
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
