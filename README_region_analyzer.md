# 区域分析器模块使用说明

## 概述

`region_analyzer.py` 是一个封装好的模块，用于分析滑动窗口区域的预测准确率，并返回准确率最高的索引范围。

## 主要功能

### 1. 单个区域分析
分析指定区域大小下准确率最高的索引范围。

### 2. 多个区域分析
同时分析多个不同区域大小，返回每个大小的最佳区域。

### 3. 详细区域信息
获取指定区域大小的前N个最佳区域的详细信息。

## 使用方法

### 基本用法

```python
from region_analyzer import analyze_best_region

# 分析区域大小为15的最佳区域
best_range = analyze_best_region(15)
print(f"最佳区域: {best_range}")  # 输出: 6605:6619
```

### 静默模式

```python
# 不显示详细信息，只返回结果
best_range = analyze_best_region(15, verbose=False)
print(best_range)  # 输出: 6605:6619
```

### 分析多个区域大小

```python
from region_analyzer import analyze_multiple_sizes

# 分析多个区域大小
results = analyze_multiple_sizes([10, 15, 20])
print(results)
# 输出: {10: '6610:6619', 15: '6605:6619', 20: '2573:2592'}
```

### 获取详细信息

```python
from region_analyzer import get_region_details

# 获取前5个最佳区域的详细信息
details = get_region_details(15, 5)
for detail in details:
    print(f"排名 {detail['rank']}: {detail['range']} (准确率: {detail['accuracy']:.2f}%)")
```

## 函数说明

### analyze_best_region(region_size=15, verbose=True)

**参数:**
- `region_size` (int): 滑动窗口大小，默认为15个索引位置
- `verbose` (bool): 是否显示详细信息，默认为True

**返回值:**
- `str`: 格式为 "start_index:end_index" 的字符串，如 "6605:6619"
- `None`: 如果没有找到有效区域

**示例:**
```python
best_range = analyze_best_region(15)
# 输出: 6605:6619
```

### analyze_multiple_sizes(sizes=[5, 10, 15, 20, 25], verbose=True)

**参数:**
- `sizes` (list): 要分析的区域大小列表
- `verbose` (bool): 是否显示详细信息，默认为True

**返回值:**
- `dict`: 键为区域大小，值为最佳区域范围的字典

**示例:**
```python
results = analyze_multiple_sizes([10, 15, 20])
# 输出: {10: '6610:6619', 15: '6605:6619', 20: '2573:2592'}
```

### get_region_details(region_size=15, top_n=5, verbose=True)

**参数:**
- `region_size` (int): 滑动窗口大小，默认为15
- `top_n` (int): 返回前N个最佳区域，默认为5
- `verbose` (bool): 是否显示详细信息，默认为True

**返回值:**
- `list`: 包含区域详细信息的字典列表

**示例:**
```python
details = get_region_details(15, 3)
# 返回前3个最佳区域的详细信息
```

## 输出格式

函数返回的索引范围格式为 `"start_index:end_index"`，例如：
- `"6605:6619"` 表示从索引6605到6619（包含两端）
- `"1234:1248"` 表示从索引1234到1248（包含两端）

## 测试示例

运行测试文件来验证模块功能：

```bash
python test_region_analyzer.py
```

## 依赖文件

该模块依赖以下文件：
- `flexible_region_analysis.py` - 核心分析功能
- `config.py` - 配置文件
- 数据文件（CSV格式）

## 注意事项

1. 确保数据文件存在且格式正确
2. 区域大小应该是正整数
3. 分析过程可能需要一些时间，取决于数据量大小
4. 返回的索引范围是基于滑动窗口模式计算的

## 错误处理

如果分析过程中出现错误，函数会：
1. 打印错误信息（如果 verbose=True）
2. 返回 None 值
3. 不会中断程序执行

## 性能说明

- 区域大小越大，分析时间越长
- 数据文件越大，处理时间越长
- 建议先用小的区域大小测试，确认功能正常后再使用大的区域大小
