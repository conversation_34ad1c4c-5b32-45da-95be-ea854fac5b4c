#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多文件处理功能
"""

from config import *

def test_file_discovery():
    """测试文件发现功能"""
    print("=== 测试多文件处理功能 ===")
    
    # 显示当前配置
    print(f"数据模式: {DATA_MODE}")
    print(f"数据目录: {DATA_DIRECTORY}")
    print(f"文件模式: {DATA_FILE_PATTERN}")
    print(f"排除文件: {EXCLUDE_FILES}")
    
    # 获取数据文件列表
    data_files = get_data_files()
    
    if data_files:
        print(f"\n✅ 成功发现 {len(data_files)} 个数据文件")
        
        # 显示每个文件的基本信息
        import os
        total_size = 0
        for i, file_path in enumerate(data_files, 1):
            file_size = os.path.getsize(file_path)
            total_size += file_size
            print(f"  {i}. {os.path.basename(file_path)}")
            print(f"     路径: {file_path}")
            print(f"     大小: {file_size / 1024 / 1024:.2f} MB")
            
            # 检查文件是否可读
            try:
                with open(file_path, 'r', encoding=FILE_ENCODING) as f:
                    first_line = f.readline().strip()
                    print(f"     状态: ✅ 可读")
                    print(f"     首行: {first_line[:50]}...")
            except Exception as e:
                print(f"     状态: ❌ 读取失败 - {e}")
            print()
        
        print(f"总文件大小: {total_size / 1024 / 1024:.2f} MB")
        
        # 估算处理时间
        estimated_time = len(data_files) * 2  # 假设每个文件2秒
        print(f"预估处理时间: {estimated_time} 秒")
        
    else:
        print("❌ 没有发现任何数据文件")
        print("\n可能的原因:")
        print("1. 当前目录没有CSV文件")
        print("2. 文件模式不匹配")
        print("3. 所有文件都在排除列表中")
        
        # 显示当前目录的所有文件
        import os
        import glob
        
        print(f"\n当前目录 ({DATA_DIRECTORY}) 的所有文件:")
        all_files = glob.glob(os.path.join(DATA_DIRECTORY, "*"))
        for file_path in sorted(all_files):
            if os.path.isfile(file_path):
                filename = os.path.basename(file_path)
                file_size = os.path.getsize(file_path) / 1024  # KB
                print(f"  {filename} ({file_size:.1f} KB)")

def test_config_modes():
    """测试不同的配置模式"""
    print("\n=== 测试不同配置模式 ===")
    
    # 保存原始配置
    original_mode = DATA_MODE
    original_pattern = DATA_FILE_PATTERN
    
    # 测试不同模式
    test_configs = [
        ('single', 'real_time_data_20250817.csv'),
        ('multiple', '*.csv'),
        ('pattern', 'real_time_data_*.csv'),
        ('pattern', '*_20250817.csv'),
    ]
    
    for mode, pattern in test_configs:
        print(f"\n测试模式: {mode}, 模式: {pattern}")
        
        # 临时修改配置
        globals()['DATA_MODE'] = mode
        if mode in ['multiple', 'pattern']:
            globals()['DATA_FILE_PATTERN'] = pattern
        
        # 获取文件列表
        files = get_data_files()
        print(f"  发现文件数: {len(files)}")
        
        if files:
            for file_path in files[:3]:  # 只显示前3个
                import os
                print(f"    - {os.path.basename(file_path)}")
            if len(files) > 3:
                print(f"    ... 还有 {len(files) - 3} 个文件")
    
    # 恢复原始配置
    globals()['DATA_MODE'] = original_mode
    globals()['DATA_FILE_PATTERN'] = original_pattern

if __name__ == "__main__":
    # 验证配置
    try:
        validate_config()
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        exit(1)
    
    # 测试文件发现
    test_file_discovery()
    
    # 测试配置模式
    test_config_modes()
    
    print("\n=== 测试完成 ===")
    print("如果发现了数据文件，您现在可以运行:")
    print("  python sorted_index_accuracy.py")
    print("  python flexible_region_analysis.py")
