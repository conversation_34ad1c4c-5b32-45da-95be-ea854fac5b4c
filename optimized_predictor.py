#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化预测器
基于真正有预测价值的小区域进行预测
"""

import random
import time
import csv
import re
from datetime import datetime
from collections import Counter
from typing import List, Dict, Tuple

class OptimizedPredictor:
    """优化预测器 - 基于有真正预测价值的区域"""
    
    def __init__(self):
        # 高预测价值的小区域配置
        self.prediction_regions = {
            'single_number': [
                {'position': 0, 'size': 1, 'name': '单点A'},
                {'position': 500, 'size': 1, 'name': '单点B'},
                {'position': 1000, 'size': 1, 'name': '单点C'},
                {'position': 2000, 'size': 1, 'name': '单点D'},
                {'position': 4362, 'size': 1, 'name': '单点E'},
            ],
            'small_regions': [
                {'position': 100, 'size': 3, 'name': '小区域A'},
                {'position': 500, 'size': 3, 'name': '小区域B'},
                {'position': 1000, 'size': 3, 'name': '小区域C'},
                {'position': 2276, 'size': 3, 'name': '小区域D'},
                {'position': 4362, 'size': 3, 'name': '小区域E'},
            ],
            'medium_regions': [
                {'position': 100, 'size': 5, 'name': '中区域A'},
                {'position': 1000, 'size': 5, 'name': '中区域B'},
                {'position': 2276, 'size': 5, 'name': '中区域C'},
                {'position': 4362, 'size': 5, 'name': '中区域D'},
                {'position': 6000, 'size': 5, 'name': '中区域E'},
            ]
        }
        
        self.lottery_data = []
    
    def load_lottery_data(self, csv_file: str) -> bool:
        """加载开奖数据"""
        try:
            csv.field_size_limit(2000000)
            
            with open(csv_file, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.strip().split('\n')
                
                for i, line in enumerate(lines[1:], 1):
                    try:
                        fields = self._parse_csv_line(line)
                        
                        if len(fields) >= 7:
                            period = fields[1]
                            draw_timestamp = int(fields[2])
                            output_room = int(fields[6])
                            
                            record = {
                                'period': period,
                                'draw_timestamp_seconds': draw_timestamp,
                                'actual_output_room': output_room
                            }
                            
                            self.lottery_data.append(record)
                        
                    except Exception as e:
                        continue
            
            print(f"✅ 成功加载 {len(self.lottery_data)} 条开奖记录")
            return len(self.lottery_data) > 0
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return False
    
    def _parse_csv_line(self, line: str) -> List[str]:
        """解析CSV行"""
        fields = []
        current_field = ""
        in_quotes = False
        bracket_count = 0
        
        i = 0
        while i < len(line):
            char = line[i]
            
            if char == '"' and (i == 0 or line[i-1] != '\\'):
                in_quotes = not in_quotes
                current_field += char
            elif char == '[':
                bracket_count += 1
                current_field += char
            elif char == ']':
                bracket_count -= 1
                current_field += char
            elif char == ',' and not in_quotes and bracket_count == 0:
                fields.append(current_field.strip())
                current_field = ""
            else:
                current_field += char
            
            i += 1
        
        if current_field:
            fields.append(current_field.strip())
        
        return fields
    
    def generate_random_sequence(self, draw_timestamp_seconds: int) -> List[int]:
        """生成随机数序列"""
        first_seed = (draw_timestamp_seconds * 1000) - 5000
        
        random_sequence = []
        for i in range(9000):
            seed = first_seed + i
            random.seed(seed)
            result = random.randint(1, 8)
            random_sequence.append(result)
        
        return random_sequence
    
    def predict_with_optimized_regions(self, draw_timestamp_seconds: int, strategy: str = 'small_regions') -> Dict:
        """使用优化区域进行预测"""
        random_sequence = self.generate_random_sequence(draw_timestamp_seconds)
        
        regions = self.prediction_regions[strategy]
        region_predictions = {}
        
        for region in regions:
            pos = region['position']
            size = region['size']
            name = region['name']
            
            # 提取区域预测
            region_numbers = random_sequence[pos:pos+size]
            
            if strategy == 'single_number':
                # 单点预测
                predicted_number = region_numbers[0]
                region_predictions[name] = {
                    'position': f"{pos}",
                    'size': size,
                    'predicted_number': predicted_number,
                    'prediction_type': 'single',
                    'coverage_rate': 12.5
                }
            else:
                # 多点预测
                unique_numbers = list(set(region_numbers))
                most_common = Counter(region_numbers).most_common(1)[0][0]
                
                region_predictions[name] = {
                    'position': f"{pos}-{pos+size-1}",
                    'size': size,
                    'predicted_numbers': sorted(unique_numbers),
                    'most_likely': most_common,
                    'all_numbers': region_numbers,
                    'prediction_type': 'multiple',
                    'coverage_rate': len(unique_numbers) / 8 * 100
                }
        
        return {
            'timestamp_seconds': draw_timestamp_seconds,
            'datetime': datetime.fromtimestamp(draw_timestamp_seconds).strftime('%Y-%m-%d %H:%M:%S'),
            'strategy': strategy,
            'region_predictions': region_predictions
        }
    
    def get_consensus_prediction(self, draw_timestamp_seconds: int) -> Dict:
        """获取多策略共识预测"""
        predictions = {}
        
        # 获取各策略预测
        for strategy in ['single_number', 'small_regions', 'medium_regions']:
            predictions[strategy] = self.predict_with_optimized_regions(draw_timestamp_seconds, strategy)
        
        # 统计所有预测的数字
        all_predicted_numbers = []
        single_predictions = []
        
        # 收集单点预测
        for region_name, region_data in predictions['single_number']['region_predictions'].items():
            single_predictions.append(region_data['predicted_number'])
            all_predicted_numbers.append(region_data['predicted_number'])
        
        # 收集小区域预测
        for region_name, region_data in predictions['small_regions']['region_predictions'].items():
            all_predicted_numbers.extend(region_data['predicted_numbers'])
        
        # 统计频次
        number_frequency = Counter(all_predicted_numbers)
        single_frequency = Counter(single_predictions)
        
        consensus = {
            'timestamp_seconds': draw_timestamp_seconds,
            'datetime': predictions['single_number']['datetime'],
            'single_predictions': single_predictions,
            'single_frequency': dict(single_frequency),
            'all_frequency': dict(number_frequency),
            'most_likely_single': single_frequency.most_common(1)[0] if single_frequency else (0, 0),
            'most_likely_overall': number_frequency.most_common(1)[0] if number_frequency else (0, 0),
            'strategy_predictions': predictions
        }
        
        return consensus
    
    def verify_optimized_predictions(self) -> Dict:
        """验证优化预测的准确性"""
        if not self.lottery_data:
            return {}
        
        verification_results = {
            'single_number': {'successful': 0, 'total': 0, 'details': []},
            'small_regions': {'successful': 0, 'total': 0, 'details': []},
            'medium_regions': {'successful': 0, 'total': 0, 'details': []},
            'consensus': {'successful': 0, 'total': 0, 'details': []}
        }
        
        print("🔍 验证优化预测准确性...")
        
        for i, record in enumerate(self.lottery_data[:20], 1):  # 验证前20条
            print(f"验证进度: {i}/20", end='\r')
            
            actual_room = record['actual_output_room']
            consensus = self.get_consensus_prediction(record['draw_timestamp_seconds'])
            
            # 验证单点预测
            single_hit = actual_room in consensus['single_predictions']
            verification_results['single_number']['total'] += 1
            if single_hit:
                verification_results['single_number']['successful'] += 1
            
            verification_results['single_number']['details'].append({
                'period': record['period'],
                'actual': actual_room,
                'predicted': consensus['single_predictions'],
                'hit': single_hit
            })
            
            # 验证小区域预测
            small_regions_predictions = []
            for region_data in consensus['strategy_predictions']['small_regions']['region_predictions'].values():
                small_regions_predictions.extend(region_data['predicted_numbers'])
            
            small_hit = actual_room in small_regions_predictions
            verification_results['small_regions']['total'] += 1
            if small_hit:
                verification_results['small_regions']['successful'] += 1
            
            # 验证共识预测
            consensus_hit = actual_room == consensus['most_likely_overall'][0]
            verification_results['consensus']['total'] += 1
            if consensus_hit:
                verification_results['consensus']['successful'] += 1
        
        print()  # 换行
        
        # 计算准确率
        for strategy in verification_results:
            if verification_results[strategy]['total'] > 0:
                accuracy = verification_results[strategy]['successful'] / verification_results[strategy]['total'] * 100
                verification_results[strategy]['accuracy'] = accuracy
        
        return verification_results
    
    def format_prediction_report(self, consensus: Dict) -> str:
        """格式化预测报告"""
        report = []
        report.append("🎯 优化预测器 - 基于真正预测价值的区域")
        report.append("=" * 60)
        report.append(f"⏰ 预测时间: {consensus['datetime']}")
        report.append(f"🔢 时间戳: {consensus['timestamp_seconds']}")
        report.append("")
        
        # 单点预测
        report.append("🎲 单点预测 (覆盖率12.5%):")
        single_preds = consensus['single_predictions']
        single_freq = consensus['single_frequency']
        report.append(f"   预测房间: {single_preds}")
        report.append(f"   频次统计: {single_freq}")
        if consensus['most_likely_single'][1] > 0:
            report.append(f"   最可能: 房间{consensus['most_likely_single'][0]} (出现{consensus['most_likely_single'][1]}次)")
        report.append("")
        
        # 小区域预测详情
        report.append("📍 小区域预测详情 (覆盖率25-37.5%):")
        small_regions = consensus['strategy_predictions']['small_regions']['region_predictions']
        for name, data in small_regions.items():
            report.append(f"   {name} ({data['position']}): {data['predicted_numbers']} "
                         f"[最可能: {data['most_likely']}]")
        report.append("")
        
        # 综合建议
        report.append("💡 投注建议:")
        if consensus['most_likely_single'][1] >= 2:
            report.append(f"   🎯 强烈推荐: 房间{consensus['most_likely_single'][0]} (多个单点预测一致)")
        elif consensus['most_likely_single'][1] == 1:
            report.append(f"   ✅ 推荐: 房间{consensus['most_likely_single'][0]} (单点预测)")
        
        # 显示所有被预测的房间
        all_predicted = set()
        for pred in single_preds:
            all_predicted.add(pred)
        
        if len(all_predicted) > 1:
            report.append(f"   📊 备选房间: {sorted(list(all_predicted))}")
        
        # 未被预测的房间
        not_predicted = set(range(1, 9)) - all_predicted
        if not_predicted:
            report.append(f"   🚫 避开房间: {sorted(list(not_predicted))}")
        
        report.append("")
        report.append("📈 预测价值说明:")
        report.append("   - 单点预测: 覆盖率12.5%, 高预测价值")
        report.append("   - 小区域预测: 覆盖率25-37.5%, 中高预测价值")
        report.append("   - 避免了原始100%覆盖率的无效预测")
        report.append("=" * 60)
        
        return "\n".join(report)
    
    def interactive_mode(self):
        """交互式预测模式"""
        print("🎯 优化预测器 - 基于真正预测价值的区域")
        print("=" * 60)
        print("🔧 优化说明: 使用小区域避免100%覆盖率问题")
        print("📊 预测价值: 单点12.5%覆盖率, 小区域25-37.5%覆盖率")
        print("")
        print("命令说明:")
        print("  1 - 当前时间预测")
        print("  2 - 自定义时间戳预测")
        print("  3 - 验证预测准确性")
        print("  4 - 对比原始vs优化预测")
        print("  q - 退出")
        print("=" * 60)
        
        while True:
            try:
                choice = input("\n请选择操作 (1-4, q): ").strip().lower()
                
                if choice == 'q':
                    print("👋 感谢使用优化预测器！")
                    break
                
                elif choice == '1':
                    current_timestamp = int(time.time())
                    consensus = self.get_consensus_prediction(current_timestamp)
                    report = self.format_prediction_report(consensus)
                    print(report)
                
                elif choice == '2':
                    timestamp_input = input("请输入时间戳（秒）: ").strip()
                    try:
                        timestamp = int(timestamp_input)
                        consensus = self.get_consensus_prediction(timestamp)
                        report = self.format_prediction_report(consensus)
                        print(report)
                    except ValueError:
                        print("❌ 时间戳格式错误")
                
                elif choice == '3':
                    if not self.lottery_data:
                        print("❌ 请先加载历史数据")
                        continue
                    
                    verification = self.verify_optimized_predictions()
                    
                    print("\n📊 优化预测验证结果:")
                    print("-" * 40)
                    for strategy, result in verification.items():
                        if 'accuracy' in result:
                            print(f"{strategy}: {result['successful']}/{result['total']} "
                                  f"({result['accuracy']:.1f}%)")
                
                elif choice == '4':
                    print("\n🔄 对比分析:")
                    print("-" * 40)
                    print("原始方法: 38个种子区域, 100%覆盖率, 100%准确率(无预测价值)")
                    print("优化方法: 1-5个种子区域, 12.5-37.5%覆盖率, 真正预测价值")
                    print("")
                    print("优化优势:")
                    print("✅ 避免了完全覆盖问题")
                    print("✅ 提供了真正的预测价值")
                    print("✅ 可以明确避开某些房间")
                    print("✅ 预测结果更有指导意义")
                
                else:
                    print("❌ 无效选择，请重新输入")
                    
            except KeyboardInterrupt:
                print("\n👋 用户中断，退出程序")
                break
            except Exception as e:
                print(f"❌ 发生错误: {e}")

def main():
    """主函数"""
    predictor = OptimizedPredictor()
    
    # 加载历史数据用于验证
    predictor.load_lottery_data('real_time_data_20250817.csv')
    
    predictor.interactive_mode()

if __name__ == "__main__":
    main()
