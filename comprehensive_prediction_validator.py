#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合预测验证器
使用综合预测方法验证历史开奖数据的准确性
"""

import csv
import re
import random
import time
from datetime import datetime
from collections import Counter, defaultdict
from typing import List, Dict, Tuple

class ComprehensivePredictionValidator:
    """综合预测验证器"""
    
    def __init__(self):
        # 基于逆向分析的热门位置
        self.hot_positions = [3244, 4692, 6103, 78, 1145, 2352, 3156, 7583, 7739, 1150]
        
        # 各房间的偏好位置
        self.room_preferred_positions = {
            1: [441, 4692, 78],
            2: [4322, 1145, 2352],
            3: [6545, 6103],
            4: [2142],
            5: [3597],
            6: [6317, 2097],
            7: [1227, 3156],
            8: [4382, 7583]
        }
        
        self.lottery_data = []
    
    def load_lottery_data(self, csv_file: str) -> bool:
        """加载开奖数据"""
        try:
            csv.field_size_limit(2000000)
            
            with open(csv_file, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.strip().split('\n')
                
                for i, line in enumerate(lines[1:], 1):
                    try:
                        fields = self._parse_csv_line(line)
                        
                        if len(fields) >= 7:
                            period = fields[1]
                            draw_timestamp = int(fields[2])
                            output_room = int(fields[6])
                            
                            record = {
                                'period': period,
                                'draw_timestamp_seconds': draw_timestamp,
                                'actual_output_room': output_room
                            }
                            
                            self.lottery_data.append(record)
                        
                    except Exception as e:
                        continue
            
            print(f"✅ 成功加载 {len(self.lottery_data)} 条开奖记录")
            return len(self.lottery_data) > 0
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return False
    
    def _parse_csv_line(self, line: str) -> List[str]:
        """解析CSV行"""
        fields = []
        current_field = ""
        in_quotes = False
        bracket_count = 0
        
        i = 0
        while i < len(line):
            char = line[i]
            
            if char == '"' and (i == 0 or line[i-1] != '\\'):
                in_quotes = not in_quotes
                current_field += char
            elif char == '[':
                bracket_count += 1
                current_field += char
            elif char == ']':
                bracket_count -= 1
                current_field += char
            elif char == ',' and not in_quotes and bracket_count == 0:
                fields.append(current_field.strip())
                current_field = ""
            else:
                current_field += char
            
            i += 1
        
        if current_field:
            fields.append(current_field.strip())
        
        return fields
    
    def calculate_first_seed(self, draw_timestamp_seconds: int) -> int:
        """计算首个种子"""
        return (draw_timestamp_seconds * 1000) - 5000
    
    def unity_random_algorithm(self, seed: int) -> int:
        """Unity Random算法"""
        random.seed(seed)
        return random.randint(1, 8)
    
    def predict_with_hot_positions(self, draw_timestamp_seconds: int) -> Dict:
        """使用热门位置进行预测"""
        first_seed = self.calculate_first_seed(draw_timestamp_seconds)
        
        hot_position_predictions = {}
        
        for position in self.hot_positions:
            seed = first_seed + position
            predicted_room = self.unity_random_algorithm(seed)
            hot_position_predictions[position] = predicted_room
        
        prediction_counts = Counter(hot_position_predictions.values())
        
        return {
            'strategy': 'hot_positions',
            'position_predictions': hot_position_predictions,
            'prediction_counts': dict(prediction_counts),
            'most_likely': prediction_counts.most_common(1)[0] if prediction_counts else (0, 0),
            'predicted_rooms': list(prediction_counts.keys())
        }
    
    def predict_with_room_preferences(self, draw_timestamp_seconds: int) -> Dict:
        """使用房间偏好位置进行预测"""
        first_seed = self.calculate_first_seed(draw_timestamp_seconds)
        
        room_self_predictions = {}
        
        for room, preferred_positions in self.room_preferred_positions.items():
            self_prediction_count = 0
            total_predictions = 0
            
            for position in preferred_positions:
                seed = first_seed + position
                predicted_room = self.unity_random_algorithm(seed)
                total_predictions += 1
                
                if predicted_room == room:
                    self_prediction_count += 1
            
            self_prediction_rate = (self_prediction_count / total_predictions * 100) if total_predictions > 0 else 0
            
            room_self_predictions[room] = {
                'self_predictions': self_prediction_count,
                'total_predictions': total_predictions,
                'self_prediction_rate': self_prediction_rate
            }
        
        return {
            'strategy': 'room_preferences',
            'room_self_predictions': room_self_predictions
        }
    
    def predict_with_position_sampling(self, draw_timestamp_seconds: int, sample_size: int = 100) -> Dict:
        """使用位置采样进行预测"""
        first_seed = self.calculate_first_seed(draw_timestamp_seconds)
        
        step = 9000 // sample_size
        sample_positions = list(range(0, 9000, step))[:sample_size]
        
        sample_predictions = {}
        
        for position in sample_positions:
            seed = first_seed + position
            predicted_room = self.unity_random_algorithm(seed)
            sample_predictions[position] = predicted_room
        
        prediction_counts = Counter(sample_predictions.values())
        
        return {
            'strategy': 'position_sampling',
            'sample_size': sample_size,
            'prediction_counts': dict(prediction_counts),
            'distribution_percentages': {room: count/sample_size*100 
                                       for room, count in prediction_counts.items()}
        }
    
    def comprehensive_prediction(self, draw_timestamp_seconds: int) -> Dict:
        """综合预测"""
        hot_prediction = self.predict_with_hot_positions(draw_timestamp_seconds)
        room_preference = self.predict_with_room_preferences(draw_timestamp_seconds)
        sampling_prediction = self.predict_with_position_sampling(draw_timestamp_seconds)
        
        # 综合分析
        comprehensive_result = {
            'timestamp_seconds': draw_timestamp_seconds,
            'hot_positions_prediction': hot_prediction,
            'room_preferences_prediction': room_preference,
            'sampling_prediction': sampling_prediction
        }
        
        # 计算综合建议
        recommendations = {
            'hot_position_top': hot_prediction['most_likely'][0] if hot_prediction['most_likely'][1] > 0 else None,
            'sampling_top': max(sampling_prediction['distribution_percentages'].items(), 
                              key=lambda x: x[1])[0] if sampling_prediction['distribution_percentages'] else None,
            'room_preference_high': []
        }
        
        # 找出自预测率高的房间
        for room, data in room_preference['room_self_predictions'].items():
            if data['self_prediction_rate'] >= 50:  # 50%以上自预测率
                recommendations['room_preference_high'].append(room)
        
        # 计算共识
        all_recommendations = []
        if recommendations['hot_position_top']:
            all_recommendations.append(recommendations['hot_position_top'])
        if recommendations['sampling_top']:
            all_recommendations.append(recommendations['sampling_top'])
        all_recommendations.extend(recommendations['room_preference_high'])
        
        consensus_counts = Counter(all_recommendations)
        
        comprehensive_result['recommendations'] = recommendations
        comprehensive_result['consensus'] = {
            'all_recommendations': all_recommendations,
            'consensus_counts': dict(consensus_counts),
            'top_consensus': consensus_counts.most_common(1)[0] if consensus_counts else (0, 0)
        }
        
        return comprehensive_result
    
    def validate_single_prediction(self, record: Dict) -> Dict:
        """验证单条记录的预测"""
        draw_timestamp = record['draw_timestamp_seconds']
        actual_room = record['actual_output_room']
        
        prediction = self.comprehensive_prediction(draw_timestamp)
        
        # 验证各种策略
        validation_result = {
            'period': record['period'],
            'actual_room': actual_room,
            'prediction': prediction,
            'validation': {}
        }
        
        # 验证热门位置预测
        hot_pred = prediction['hot_positions_prediction']
        hot_hit = actual_room in hot_pred['predicted_rooms']
        hot_top_hit = actual_room == hot_pred['most_likely'][0] if hot_pred['most_likely'][1] > 0 else False
        
        validation_result['validation']['hot_positions'] = {
            'hit_in_predictions': hot_hit,
            'hit_top_prediction': hot_top_hit,
            'predicted_rooms': hot_pred['predicted_rooms'],
            'top_prediction': hot_pred['most_likely'][0] if hot_pred['most_likely'][1] > 0 else None
        }
        
        # 验证采样预测
        sampling_pred = prediction['sampling_prediction']
        sampling_percentage = sampling_pred['distribution_percentages'].get(actual_room, 0)
        sampling_top = max(sampling_pred['distribution_percentages'].items(), key=lambda x: x[1])[0]
        sampling_top_hit = actual_room == sampling_top
        
        validation_result['validation']['sampling'] = {
            'actual_room_percentage': sampling_percentage,
            'top_prediction': sampling_top,
            'hit_top_prediction': sampling_top_hit,
            'above_average': sampling_percentage > 12.5  # 高于理论平均12.5%
        }
        
        # 验证房间偏好
        room_pref = prediction['room_preferences_prediction']
        actual_room_self_rate = room_pref['room_self_predictions'].get(actual_room, {}).get('self_prediction_rate', 0)
        
        validation_result['validation']['room_preference'] = {
            'actual_room_self_rate': actual_room_self_rate,
            'high_self_prediction': actual_room_self_rate >= 50
        }
        
        # 验证综合共识
        consensus = prediction['consensus']
        consensus_hit = actual_room == consensus['top_consensus'][0] if consensus['top_consensus'][1] > 0 else False
        consensus_in_recommendations = actual_room in consensus['all_recommendations']
        
        validation_result['validation']['consensus'] = {
            'hit_top_consensus': consensus_hit,
            'in_recommendations': consensus_in_recommendations,
            'top_consensus': consensus['top_consensus'][0] if consensus['top_consensus'][1] > 0 else None,
            'consensus_strength': consensus['top_consensus'][1] if consensus['top_consensus'][1] > 0 else 0
        }
        
        return validation_result
    
    def validate_all_predictions(self) -> Dict:
        """验证所有预测"""
        print("🔍 验证综合预测方法对历史数据的准确性...")
        
        validation_results = []
        strategy_stats = {
            'hot_positions': {'hit_in_predictions': 0, 'hit_top_prediction': 0, 'total': 0},
            'sampling': {'hit_top_prediction': 0, 'above_average': 0, 'total': 0},
            'room_preference': {'high_self_prediction': 0, 'total': 0},
            'consensus': {'hit_top_consensus': 0, 'in_recommendations': 0, 'total': 0}
        }
        
        for i, record in enumerate(self.lottery_data, 1):
            print(f"验证进度: {i}/{len(self.lottery_data)}", end='\r')
            
            validation = self.validate_single_prediction(record)
            validation_results.append(validation)
            
            # 统计各策略表现
            val = validation['validation']
            
            # 热门位置统计
            if val['hot_positions']['hit_in_predictions']:
                strategy_stats['hot_positions']['hit_in_predictions'] += 1
            if val['hot_positions']['hit_top_prediction']:
                strategy_stats['hot_positions']['hit_top_prediction'] += 1
            strategy_stats['hot_positions']['total'] += 1
            
            # 采样统计
            if val['sampling']['hit_top_prediction']:
                strategy_stats['sampling']['hit_top_prediction'] += 1
            if val['sampling']['above_average']:
                strategy_stats['sampling']['above_average'] += 1
            strategy_stats['sampling']['total'] += 1
            
            # 房间偏好统计
            if val['room_preference']['high_self_prediction']:
                strategy_stats['room_preference']['high_self_prediction'] += 1
            strategy_stats['room_preference']['total'] += 1
            
            # 共识统计
            if val['consensus']['hit_top_consensus']:
                strategy_stats['consensus']['hit_top_consensus'] += 1
            if val['consensus']['in_recommendations']:
                strategy_stats['consensus']['in_recommendations'] += 1
            strategy_stats['consensus']['total'] += 1
        
        print()  # 换行
        
        # 计算准确率
        for strategy, stats in strategy_stats.items():
            metrics_to_process = [(metric, count) for metric, count in stats.items() if metric != 'total']
            for metric, count in metrics_to_process:
                if stats['total'] > 0:
                    accuracy = count / stats['total'] * 100
                    stats[f'{metric}_accuracy'] = accuracy
        
        return {
            'validation_results': validation_results,
            'strategy_stats': strategy_stats,
            'total_records': len(self.lottery_data)
        }
    
    def generate_validation_report(self, validation_summary: Dict) -> str:
        """生成验证报告"""
        report = []
        report.append("🔍 综合预测方法验证报告")
        report.append("=" * 70)
        report.append(f"📊 验证记录数: {validation_summary['total_records']}")
        report.append("")
        
        stats = validation_summary['strategy_stats']
        
        # 热门位置策略验证
        hot_stats = stats['hot_positions']
        report.append("🔥 热门位置策略验证:")
        report.append(f"   包含实际房间: {hot_stats['hit_in_predictions']}/{hot_stats['total']} "
                     f"({hot_stats.get('hit_in_predictions_accuracy', 0):.1f}%)")
        report.append(f"   命中最可能房间: {hot_stats['hit_top_prediction']}/{hot_stats['total']} "
                     f"({hot_stats.get('hit_top_prediction_accuracy', 0):.1f}%)")
        report.append("")
        
        # 位置采样策略验证
        sampling_stats = stats['sampling']
        report.append("📊 位置采样策略验证:")
        report.append(f"   命中最可能房间: {sampling_stats['hit_top_prediction']}/{sampling_stats['total']} "
                     f"({sampling_stats.get('hit_top_prediction_accuracy', 0):.1f}%)")
        report.append(f"   实际房间高于平均: {sampling_stats['above_average']}/{sampling_stats['total']} "
                     f"({sampling_stats.get('above_average_accuracy', 0):.1f}%)")
        report.append("")
        
        # 房间偏好策略验证
        pref_stats = stats['room_preference']
        report.append("🎲 房间偏好策略验证:")
        report.append(f"   高自预测率房间: {pref_stats['high_self_prediction']}/{pref_stats['total']} "
                     f"({pref_stats.get('high_self_prediction_accuracy', 0):.1f}%)")
        report.append("")
        
        # 综合共识验证
        consensus_stats = stats['consensus']
        report.append("🎯 综合共识验证:")
        report.append(f"   命中共识房间: {consensus_stats['hit_top_consensus']}/{consensus_stats['total']} "
                     f"({consensus_stats.get('hit_top_consensus_accuracy', 0):.1f}%)")
        report.append(f"   在推荐范围内: {consensus_stats['in_recommendations']}/{consensus_stats['total']} "
                     f"({consensus_stats.get('in_recommendations_accuracy', 0):.1f}%)")
        report.append("")
        
        # 对比理论随机
        theoretical_accuracy = 100 / 8  # 12.5%
        report.append("🧮 与理论随机对比:")
        report.append(f"   理论随机准确率: {theoretical_accuracy:.1f}%")
        
        best_accuracy = max([
            hot_stats.get('hit_top_prediction_accuracy', 0),
            sampling_stats.get('hit_top_prediction_accuracy', 0),
            consensus_stats.get('hit_top_consensus_accuracy', 0)
        ])
        
        report.append(f"   最佳策略准确率: {best_accuracy:.1f}%")
        report.append(f"   提升幅度: {best_accuracy - theoretical_accuracy:+.1f}%")
        
        if best_accuracy > theoretical_accuracy * 1.2:
            report.append("   ✅ 综合预测显著优于随机!")
        elif best_accuracy > theoretical_accuracy * 1.05:
            report.append("   ⚠️ 综合预测略优于随机")
        else:
            report.append("   ❌ 综合预测接近随机水平")
        
        report.append("")
        
        # 最佳策略建议
        report.append("💡 最佳策略建议:")
        
        if consensus_stats.get('in_recommendations_accuracy', 0) > 20:
            report.append("   🎯 推荐使用综合共识策略")
        elif sampling_stats.get('above_average_accuracy', 0) > 60:
            report.append("   📊 推荐关注位置采样分布偏差")
        elif hot_stats.get('hit_in_predictions_accuracy', 0) > 30:
            report.append("   🔥 推荐使用热门位置覆盖策略")
        else:
            report.append("   ⚠️ 所有策略效果有限，建议谨慎使用")
        
        report.append("=" * 70)
        
        return "\n".join(report)

def main():
    """主函数"""
    print("🔍 综合预测验证器")
    print("=" * 60)
    print("验证综合预测方法对历史开奖数据的准确性")
    print()
    
    # 创建验证器
    validator = ComprehensivePredictionValidator()
    
    # 加载数据
    if not validator.load_lottery_data('real_time_data_20250817.csv'):
        return
    
    print()
    
    # 验证所有预测
    validation_summary = validator.validate_all_predictions()
    
    # 生成报告
    report = validator.generate_validation_report(validation_summary)
    print(report)
    
    # 保存报告
    with open('comprehensive_prediction_validation_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n💾 详细验证报告已保存到: comprehensive_prediction_validation_report.txt")
    
    # 显示详细案例
    print(f"\n🔍 详细验证案例 (前5条):")
    print("-" * 60)
    
    for i, validation in enumerate(validation_summary['validation_results'][:5], 1):
        val = validation['validation']
        print(f"案例 {i} (期号{validation['period']}):")
        print(f"   实际房间: {validation['actual_room']}")
        print(f"   热门位置预测: {val['hot_positions']['predicted_rooms']} "
              f"[最可能: {val['hot_positions']['top_prediction']}]")
        print(f"   采样最可能: 房间{val['sampling']['top_prediction']} "
              f"[实际房间占比: {val['sampling']['actual_room_percentage']:.1f}%]")
        print(f"   共识推荐: {val['consensus']['top_consensus']} "
              f"[强度: {val['consensus']['consensus_strength']}]")
        
        # 显示命中情况
        hits = []
        if val['hot_positions']['hit_in_predictions']:
            hits.append("热门位置✅")
        if val['sampling']['above_average']:
            hits.append("采样高于平均✅")
        if val['consensus']['in_recommendations']:
            hits.append("共识推荐✅")
        
        print(f"   命中情况: {' '.join(hits) if hits else '无命中❌'}")
        print()

if __name__ == "__main__":
    main()
