#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
区域分析器使用示例

展示如何使用 region_analyzer 模块进行滑动窗口区域分析
"""

from region_analyzer import analyze_best_region, analyze_multiple_sizes, get_region_details

def example_1_basic_usage():
    """示例1: 基本用法 - 分析单个区域大小"""
    print("=" * 60)
    print("示例1: 基本用法 - 分析区域大小为15的最佳区域")
    print("=" * 60)
    
    # 分析区域大小为15的最佳区域
    best_range = analyze_best_region(15)
    
    if best_range:
        print(f"\n🎯 结果: {best_range}")
        print(f"   解释: 索引位置 {best_range.split(':')[0]} 到 {best_range.split(':')[1]} 的区域准确率最高")
    else:
        print("\n❌ 未找到有效结果")

def example_2_silent_mode():
    """示例2: 静默模式 - 不显示详细过程"""
    print("\n" + "=" * 60)
    print("示例2: 静默模式 - 只获取结果，不显示分析过程")
    print("=" * 60)
    
    # 静默模式分析
    best_range = analyze_best_region(15, verbose=False)
    
    if best_range:
        print(f"🎯 最佳区域: {best_range}")
    else:
        print("❌ 未找到有效结果")

def example_3_multiple_sizes():
    """示例3: 分析多个区域大小"""
    print("\n" + "=" * 60)
    print("示例3: 分析多个区域大小")
    print("=" * 60)
    
    # 分析多个区域大小
    sizes = [10, 15, 20]
    results = analyze_multiple_sizes(sizes, verbose=False)
    
    print("📊 多个区域大小的分析结果:")
    for size, range_str in results.items():
        if range_str:
            print(f"   区域大小 {size:2d}: {range_str}")
        else:
            print(f"   区域大小 {size:2d}: 无有效结果")

def example_4_detailed_info():
    """示例4: 获取详细信息"""
    print("\n" + "=" * 60)
    print("示例4: 获取前3个最佳区域的详细信息")
    print("=" * 60)
    
    # 获取详细信息
    details = get_region_details(15, 3, verbose=False)
    
    if details:
        print("🏆 前3个最佳区域:")
        print("排名 | 索引范围    | 准确率  | 命中次数")
        print("-" * 40)
        for detail in details:
            print(f" {detail['rank']:2d}  | {detail['range']:11s} | {detail['accuracy']:6.2f}% | {detail['hit_count']:4d}/{detail['total_count']:4d}")
    else:
        print("❌ 未找到详细信息")

def example_5_practical_usage():
    """示例5: 实际应用场景"""
    print("\n" + "=" * 60)
    print("示例5: 实际应用场景 - 快速获取最佳区域")
    print("=" * 60)
    
    def get_best_region_for_prediction(window_size):
        """获取指定窗口大小的最佳预测区域"""
        result = analyze_best_region(window_size, verbose=False)
        if result:
            start, end = result.split(':')
            return int(start), int(end)
        return None, None
    
    # 获取不同窗口大小的最佳区域
    window_sizes = [10, 15, 20]
    
    print("🎯 实际应用结果:")
    for size in window_sizes:
        start_idx, end_idx = get_best_region_for_prediction(size)
        if start_idx is not None:
            print(f"   窗口大小 {size:2d}: 使用索引 {start_idx} 到 {end_idx} 进行预测")
        else:
            print(f"   窗口大小 {size:2d}: 无有效预测区域")

def main():
    """主函数 - 运行所有示例"""
    print("🚀 区域分析器使用示例")
    print("本示例将展示如何使用 region_analyzer 模块")
    
    try:
        # 运行所有示例
        example_1_basic_usage()
        example_2_silent_mode()
        example_3_multiple_sizes()
        example_4_detailed_info()
        example_5_practical_usage()
        
        print("\n" + "=" * 60)
        print("✅ 所有示例运行完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 运行示例时出现错误: {e}")
        print("请检查数据文件是否存在，以及模块是否正确安装。")

if __name__ == "__main__":
    main()
