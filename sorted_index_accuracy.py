#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
索引位置准确率统计系统 - 模式1

功能描述:
- 统计预测号码列表中每个索引位置(0-8999)的准确率
- 按准确率从高到低排序，识别表现最佳的索引位置
- 生成详细的统计报告

使用方法:
    python sorted_index_accuracy.py

输出文件:
    sorted_index_accuracy_report.txt - 完整的索引位置准确率排序报告

统计逻辑:
    对于每条记录中的每个索引位置i:
        如果 predict_numbers[i] == actual_number:
            索引i命中次数 += 1
        索引i总出现次数 += 1

    索引i准确率 = 命中次数 / 总出现次数 * 100%

作者: 预测分析系统
版本: 2.0
"""

import csv
import ast
import sys
from collections import defaultdict
from config import *

# 增加CSV字段大小限制
csv.field_size_limit(CSV_FIELD_SIZE_LIMIT)

def analyze_and_sort_by_accuracy():
    """分析索引位置准确率并按准确率排序"""
    data_files = get_data_files()

    if not data_files:
        print("❌ 没有找到数据文件")
        return []

    print("=== 按准确率排序的索引位置统计 ===")
    print(f"📊 处理 {len(data_files)} 个数据文件")

    # 统计每个索引位置的情况
    index_stats = defaultdict(lambda: {'total_count': 0, 'hit_count': 0})

    total_records = 0
    valid_records = 0
    processed_files = 0

    for file_idx, filename in enumerate(data_files):
        print(f"\n📄 处理文件 {file_idx + 1}/{len(data_files)}: {filename}")

        try:
            with open(filename, 'r', encoding=FILE_ENCODING) as file:
                reader = csv.reader(file)
                headers = next(reader)

                file_records = 0
                file_valid_records = 0

                for row_num, row in enumerate(reader):
                    total_records += 1
                    file_records += 1

                    try:
                        period = row[1]
                        timestamp = row[0]

                        # 提取预测号码序列
                        predict_numbers = []
                        if len(row) > 4 and row[4]:
                            predict_str = row[4].strip()
                            if predict_str and predict_str.startswith('[') and predict_str.endswith(']'):
                                try:
                                    predict_numbers = ast.literal_eval(predict_str)
                                except Exception as e:
                                    continue

                        # 提取开出的号码
                        actual_number = None
                        if len(row) > 6 and row[6]:
                            try:
                                actual_number = int(row[6])
                            except:
                                continue

                        # 只处理有效的记录
                        if predict_numbers and actual_number is not None:
                            valid_records += 1
                            file_valid_records += 1

                            # 分析每个索引位置
                            for index, predicted_num in enumerate(predict_numbers):
                                # 统计每个索引位置的总出现次数
                                index_stats[index]['total_count'] += 1

                                # 如果这个位置的号码等于开出号码，则命中
                                if predicted_num == actual_number:
                                    index_stats[index]['hit_count'] += 1

                    except Exception as e:
                        print(f"文件 {filename} 第{row_num + 1}行处理出错: {e}")

                print(f"  ✅ 文件处理完成: {file_records}条记录，{file_valid_records}条有效")
                processed_files += 1

        except Exception as e:
            print(f"❌ 文件 {filename} 处理失败: {e}")
            continue

    print(f"\n📊 合并统计结果:")
    print(f"处理文件数: {processed_files}/{len(data_files)}")
    print(f"总记录数: {total_records}")
    print(f"有效记录数: {valid_records}")
    
    # 计算每个索引位置的准确率并排序
    index_accuracies = []
    for index in index_stats:
        total_count = index_stats[index]['total_count']
        hit_count = index_stats[index]['hit_count']
        accuracy = hit_count / total_count * 100 if total_count > 0 else 0
        
        index_accuracies.append({
            'index': index,
            'total_count': total_count,
            'hit_count': hit_count,
            'accuracy': accuracy
        })
    
    # 按准确率从高到低排序
    index_accuracies.sort(key=lambda x: x['accuracy'], reverse=True)
    
    # 显示排序后的结果
    print(f"\n=== 各索引位置准确率统计（按准确率排序）===")
    print("排名 | 索引 | 总出现次数 | 命中次数 | 准确率")
    print("-" * 50)
    
    # 显示前N名
    for rank, item in enumerate(index_accuracies[:TOP_INDEX_DISPLAY_COUNT], 1):
        print(f"{rank:3d}  | {item['index']:4d} |     {item['total_count']:3d}      |   {item['hit_count']:3d}    | {item['accuracy']:6.2f}%")
    
    print(f"\n显示了前{TOP_INDEX_DISPLAY_COUNT}名，总共有{len(index_accuracies)}个索引位置")
    
    # 显示一些统计信息
    print(f"\n=== 准确率统计信息 ===")
    accuracies = [item['accuracy'] for item in index_accuracies]
    print(f"最高准确率: {max(accuracies):.2f}%")
    print(f"最低准确率: {min(accuracies):.2f}%")
    print(f"平均准确率: {sum(accuracies)/len(accuracies):.2f}%")
    
    # 统计不同准确率区间的索引数量
    print(f"\n=== 准确率区间分布 ===")
    ranges = [
        (20, float('inf'), "20%以上"),
        (15, 20, "15%-20%"),
        (10, 15, "10%-15%"),
        (5, 10, "5%-10%"),
        (0, 5, "0%-5%")
    ]
    
    for min_acc, max_acc, label in ranges:
        count = sum(1 for acc in accuracies if min_acc <= acc < max_acc)
        percentage = count / len(accuracies) * 100
        print(f"{label}: {count}个索引位置 ({percentage:.1f}%)")
    
    # 保存完整的排序结果
    save_sorted_results(index_accuracies, valid_records)
    
    return index_accuracies

def save_sorted_results(index_accuracies, valid_records):
    """保存按准确率排序的完整结果"""
    report_filename = 'sorted_index_accuracy_report.txt'
    
    with open(report_filename, 'w', encoding='utf-8') as f:
        f.write("=== 按准确率排序的索引位置统计报告 ===\n")
        f.write(f"生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"分析的有效记录数: {valid_records}\n\n")
        
        f.write("=== 各索引位置准确率统计（按准确率排序）===\n")
        f.write("排名 | 索引 | 总出现次数 | 命中次数 | 准确率\n")
        f.write("-" * 50 + "\n")
        
        # 保存所有结果
        for rank, item in enumerate(index_accuracies, 1):
            f.write(f"{rank:3d}  | {item['index']:4d} |     {item['total_count']:3d}      |   {item['hit_count']:3d}    | {item['accuracy']:6.2f}%\n")
        
        # 统计信息
        accuracies = [item['accuracy'] for item in index_accuracies]
        f.write(f"\n=== 统计摘要 ===\n")
        f.write(f"总索引位置数: {len(index_accuracies)}\n")
        f.write(f"最高准确率: {max(accuracies):.2f}% (索引{index_accuracies[0]['index']})\n")
        f.write(f"最低准确率: {min(accuracies):.2f}% (索引{index_accuracies[-1]['index']})\n")
        f.write(f"平均准确率: {sum(accuracies)/len(accuracies):.2f}%\n")
        
        # 准确率区间分布
        f.write(f"\n=== 准确率区间分布 ===\n")
        ranges = [
            (20, float('inf'), "20%以上"),
            (15, 20, "15%-20%"),
            (10, 15, "10%-15%"),
            (5, 10, "5%-10%"),
            (0, 5, "0%-5%")
        ]
        
        for min_acc, max_acc, label in ranges:
            count = sum(1 for acc in accuracies if min_acc <= acc < max_acc)
            percentage = count / len(accuracies) * 100
            f.write(f"{label}: {count}个索引位置 ({percentage:.1f}%)\n")
        
        # 前100名详细列表
        f.write(f"\n=== 前100名索引位置详细列表 ===\n")
        for rank, item in enumerate(index_accuracies[:100], 1):
            f.write(f"第{rank:2d}名: 索引{item['index']:4d} - {item['hit_count']:2d}/{item['total_count']:3d} = {item['accuracy']:5.2f}%\n")
    
    print(f"完整排序结果已保存到: {report_filename}")

if __name__ == "__main__":
    # 分析并按准确率排序
    sorted_results = analyze_and_sort_by_accuracy()
    
    print(f"\n=== 分析完成 ===")
    print(f"已按准确率从高到低排序所有索引位置")
    print(f"详细结果已保存到文件中")
