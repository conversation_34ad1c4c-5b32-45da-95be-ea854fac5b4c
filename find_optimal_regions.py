#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
寻找最优预测区域
寻找有真正预测价值的区域（不完全覆盖1-8但准确率高）
"""

import csv
import re
import random
from collections import Counter
from typing import List, Dict, <PERSON><PERSON>

def parse_csv_line(line: str) -> List[str]:
    """解析CSV行"""
    fields = []
    current_field = ""
    in_quotes = False
    bracket_count = 0
    
    i = 0
    while i < len(line):
        char = line[i]
        
        if char == '"' and (i == 0 or line[i-1] != '\\'):
            in_quotes = not in_quotes
            current_field += char
        elif char == '[':
            bracket_count += 1
            current_field += char
        elif char == ']':
            bracket_count -= 1
            current_field += char
        elif char == ',' and not in_quotes and bracket_count == 0:
            fields.append(current_field.strip())
            current_field = ""
        else:
            current_field += char
        
        i += 1
    
    if current_field:
        fields.append(current_field.strip())
    
    return fields

def generate_random_sequence(draw_timestamp_seconds: int) -> List[int]:
    """生成随机数序列"""
    first_seed = (draw_timestamp_seconds * 1000) - 5000
    
    random_sequence = []
    for i in range(9000):
        seed = first_seed + i
        random.seed(seed)
        result = random.randint(1, 8)
        random_sequence.append(result)
    
    return random_sequence

def evaluate_region(start: int, end: int, records: List[Dict]) -> Dict:
    """评估区域的预测价值"""
    region_size = end - start + 1
    
    successful_predictions = 0
    all_room_sets = []
    room_frequency = Counter()
    
    for record in records:
        random_sequence = generate_random_sequence(record['draw_timestamp_seconds'])
        region_predictions = random_sequence[start:end+1]
        unique_rooms = set(region_predictions)
        
        # 检查是否包含实际房间
        actual_room = record['actual_output_room']
        if actual_room in region_predictions:
            successful_predictions += 1
        
        # 统计房间频次
        for room in region_predictions:
            room_frequency[room] += 1
        
        all_room_sets.append(unique_rooms)
    
    # 计算统计指标
    total_records = len(records)
    accuracy_rate = (successful_predictions / total_records * 100) if total_records > 0 else 0
    
    # 分析房间覆盖
    all_covered_rooms = set()
    for room_set in all_room_sets:
        all_covered_rooms |= room_set
    
    coverage_rate = len(all_covered_rooms) / 8 * 100
    
    # 计算房间分布的均匀性（标准差）
    total_predictions = sum(room_frequency.values())
    room_percentages = [room_frequency.get(i, 0) / total_predictions * 100 for i in range(1, 9)]
    avg_percentage = sum(room_percentages) / 8
    variance = sum((p - avg_percentage) ** 2 for p in room_percentages) / 8
    std_dev = variance ** 0.5
    
    return {
        'start': start,
        'end': end,
        'size': region_size,
        'accuracy_rate': accuracy_rate,
        'coverage_rate': coverage_rate,
        'covered_rooms': sorted(list(all_covered_rooms)),
        'room_frequency': dict(room_frequency),
        'room_percentages': room_percentages,
        'distribution_std': std_dev,
        'prediction_value': accuracy_rate * (100 - coverage_rate) / 100,  # 预测价值 = 准确率 × (1 - 覆盖率)
        'successful_predictions': successful_predictions,
        'total_records': total_records
    }

def find_best_regions(records: List[Dict], max_regions: int = 20) -> List[Dict]:
    """寻找最佳预测区域"""
    print("🔍 搜索最佳预测区域...")
    
    best_regions = []
    
    # 测试不同大小的区域
    for size in [1, 2, 3, 4, 5, 8, 10, 15, 20]:
        print(f"   测试区域大小: {size}")
        
        size_best_regions = []
        
        # 在9000个位置中搜索（每100个位置测试一次以节省时间）
        for start in range(0, 9000 - size, 100):
            end = start + size - 1
            
            evaluation = evaluate_region(start, end, records)
            
            # 筛选条件：准确率 >= 80% 且 覆盖率 < 100%
            if evaluation['accuracy_rate'] >= 80 and evaluation['coverage_rate'] < 100:
                size_best_regions.append(evaluation)
        
        # 按预测价值排序，取前几个
        size_best_regions.sort(key=lambda x: x['prediction_value'], reverse=True)
        best_regions.extend(size_best_regions[:3])  # 每个大小取前3个
    
    # 按预测价值排序，返回最佳的
    best_regions.sort(key=lambda x: x['prediction_value'], reverse=True)
    
    return best_regions[:max_regions]

def find_single_number_regions(records: List[Dict]) -> List[Dict]:
    """寻找只预测单一数字的区域"""
    print("🎯 搜索单一数字预测区域...")
    
    single_regions = []
    
    # 测试小区域
    for size in [1, 2, 3]:
        for start in range(0, 9000 - size, 200):  # 每200个位置测试一次
            end = start + size - 1
            
            # 检查是否总是预测相同数字
            predicted_numbers = set()
            
            for record in records[:5]:  # 只测试前5条记录以节省时间
                random_sequence = generate_random_sequence(record['draw_timestamp_seconds'])
                region_predictions = random_sequence[start:end+1]
                
                # 取最频繁的数字
                most_common = Counter(region_predictions).most_common(1)[0][0]
                predicted_numbers.add(most_common)
            
            # 如果总是预测相同数字
            if len(predicted_numbers) == 1:
                predicted_number = list(predicted_numbers)[0]
                
                # 计算准确率
                successful = 0
                for record in records:
                    if record['actual_output_room'] == predicted_number:
                        successful += 1
                
                accuracy = successful / len(records) * 100
                
                if accuracy > 0:  # 至少有一些准确性
                    single_regions.append({
                        'start': start,
                        'end': end,
                        'size': size,
                        'predicted_number': predicted_number,
                        'accuracy_rate': accuracy,
                        'successful_predictions': successful,
                        'total_records': len(records)
                    })
    
    # 按准确率排序
    single_regions.sort(key=lambda x: x['accuracy_rate'], reverse=True)
    
    return single_regions[:10]

def main():
    """主函数"""
    print("🔍 寻找最优预测区域")
    print("=" * 50)
    print("寻找有真正预测价值的区域（不完全覆盖但准确率高）")
    print()
    
    # 加载数据
    csv.field_size_limit(2000000)
    
    records = []
    with open('real_time_data_20250817.csv', 'r', encoding='utf-8') as f:
        content = f.read()
        lines = content.strip().split('\n')
        
        for i, line in enumerate(lines[1:51], 1):  # 取前50条记录
            try:
                fields = parse_csv_line(line)
                
                if len(fields) >= 7:
                    period = fields[1]
                    draw_timestamp = int(fields[2])
                    output_room = int(fields[6])
                    
                    records.append({
                        'period': period,
                        'draw_timestamp_seconds': draw_timestamp,
                        'actual_output_room': output_room
                    })
                    
            except Exception as e:
                continue
    
    print(f"✅ 加载了 {len(records)} 条测试记录")
    print()
    
    # 寻找最佳区域
    best_regions = find_best_regions(records)
    
    print("🏆 最佳预测区域:")
    print("-" * 60)
    
    if best_regions:
        for i, region in enumerate(best_regions[:10], 1):
            print(f"{i}. 区域 {region['start']}-{region['end']} (大小: {region['size']}):")
            print(f"   准确率: {region['accuracy_rate']:.1f}%")
            print(f"   覆盖率: {region['coverage_rate']:.1f}%")
            print(f"   覆盖房间: {region['covered_rooms']}")
            print(f"   预测价值: {region['prediction_value']:.1f}")
            print(f"   成功预测: {region['successful_predictions']}/{region['total_records']}")
            print()
    else:
        print("❌ 未找到符合条件的区域（准确率>=80%且覆盖率<100%）")
    
    # 寻找单一数字预测区域
    single_regions = find_single_number_regions(records)
    
    print("🎯 单一数字预测区域:")
    print("-" * 60)
    
    if single_regions:
        for i, region in enumerate(single_regions[:5], 1):
            print(f"{i}. 区域 {region['start']}-{region['end']} (大小: {region['size']}):")
            print(f"   总是预测: 房间{region['predicted_number']}")
            print(f"   准确率: {region['accuracy_rate']:.1f}%")
            print(f"   成功预测: {region['successful_predictions']}/{region['total_records']}")
            print()
    else:
        print("❌ 未找到单一数字预测区域")
    
    # 分析原始"100%准确率"区域的问题
    print("⚠️ 原始区域问题分析:")
    print("-" * 60)
    
    original_region = evaluate_region(2258, 2295, records)
    print(f"原始区域A (2258-2295):")
    print(f"   准确率: {original_region['accuracy_rate']:.1f}%")
    print(f"   覆盖率: {original_region['coverage_rate']:.1f}%")
    print(f"   覆盖房间: {original_region['covered_rooms']}")
    print(f"   预测价值: {original_region['prediction_value']:.1f}")
    print(f"   问题: 覆盖率100%意味着这不是真正的预测，而是完全覆盖")
    print()
    
    print("💡 结论:")
    print("=" * 50)
    print("1. 原始的100%准确率区域确实是因为完全覆盖1-8所有房间")
    print("2. 需要寻找覆盖率<100%但准确率仍然较高的区域")
    print("3. 真正的预测价值 = 准确率 × (1 - 覆盖率)")
    print("4. 建议使用预测价值最高的区域进行实际预测")

if __name__ == "__main__":
    main()
