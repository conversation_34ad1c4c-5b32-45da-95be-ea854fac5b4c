#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单区域测试
快速测试不同大小区域的覆盖情况
"""

import random

def test_region_coverage(size: int, position: int = 2276) -> dict:
    """测试指定大小和位置的区域覆盖情况"""
    # 使用一个固定的时间戳进行测试
    test_timestamp = 1755424371
    first_seed = (test_timestamp * 1000) - 5000
    
    # 生成指定区域的随机数
    region_numbers = []
    for i in range(position, position + size):
        seed = first_seed + i
        random.seed(seed)
        result = random.randint(1, 8)
        region_numbers.append(result)
    
    # 分析覆盖情况
    unique_numbers = set(region_numbers)
    coverage_rate = len(unique_numbers) / 8 * 100
    
    return {
        'size': size,
        'position': position,
        'numbers': region_numbers,
        'unique_numbers': sorted(list(unique_numbers)),
        'coverage_rate': coverage_rate,
        'missing_numbers': sorted(list(set(range(1, 9)) - unique_numbers))
    }

def main():
    """主函数"""
    print("🔍 简单区域覆盖测试")
    print("=" * 40)
    
    # 测试不同大小的区域
    sizes_to_test = [1, 2, 3, 4, 5, 8, 10, 15, 20, 25, 30, 38]
    
    print("📊 不同区域大小的覆盖情况:")
    print("-" * 40)
    
    for size in sizes_to_test:
        result = test_region_coverage(size)
        
        print(f"区域大小 {size:2d}: 覆盖 {result['coverage_rate']:5.1f}% | "
              f"包含房间 {result['unique_numbers']} | "
              f"缺失房间 {result['missing_numbers']}")
        
        if result['coverage_rate'] == 100.0:
            print(f"             ⚠️ 完全覆盖 - 无预测价值")
        elif result['coverage_rate'] >= 75.0:
            print(f"             ⚡ 高覆盖 - 预测价值较低")
        elif result['coverage_rate'] >= 50.0:
            print(f"             ✅ 中等覆盖 - 有一定预测价值")
        else:
            print(f"             🎯 低覆盖 - 高预测价值")
    
    print()
    
    # 测试不同位置的小区域
    print("🎯 测试不同位置的小区域 (大小=3):")
    print("-" * 40)
    
    positions_to_test = [100, 500, 1000, 2000, 2276, 3000, 4000, 4362, 5000, 6000, 7000, 8000]
    
    for pos in positions_to_test:
        if pos + 3 <= 9000:  # 确保不超出范围
            result = test_region_coverage(3, pos)
            
            print(f"位置 {pos:4d}: 覆盖 {result['coverage_rate']:5.1f}% | "
                  f"包含房间 {result['unique_numbers']} | "
                  f"数字序列 {result['numbers']}")
    
    print()
    
    # 寻找最小覆盖区域
    print("🔍 寻找最小覆盖区域:")
    print("-" * 40)
    
    min_coverage_found = 100.0
    best_small_regions = []
    
    # 测试大小1-10的区域在不同位置
    for size in range(1, 11):
        for pos in range(0, 9000, 500):  # 每500个位置测试一次
            if pos + size <= 9000:
                result = test_region_coverage(size, pos)
                
                if result['coverage_rate'] < min_coverage_found:
                    min_coverage_found = result['coverage_rate']
                    best_small_regions = [result]
                elif result['coverage_rate'] == min_coverage_found and len(best_small_regions) < 5:
                    best_small_regions.append(result)
    
    print(f"最小覆盖率: {min_coverage_found:.1f}%")
    print("最佳小区域:")
    
    for i, region in enumerate(best_small_regions[:5], 1):
        print(f"  {i}. 位置 {region['position']}-{region['position']+region['size']-1} "
              f"(大小{region['size']}): 覆盖{region['coverage_rate']:.1f}% | "
              f"房间 {region['unique_numbers']}")
    
    print()
    print("💡 结论:")
    print("=" * 40)
    
    if min_coverage_found >= 100.0:
        print("❌ 即使是最小的区域也完全覆盖所有房间")
        print("   这说明随机数分布过于均匀，难以找到有预测价值的区域")
    elif min_coverage_found >= 75.0:
        print("⚠️ 最小覆盖率仍然很高，预测价值有限")
    else:
        print("✅ 找到了有预测价值的小区域")
        print(f"   最小覆盖率: {min_coverage_found:.1f}%")
        print("   建议使用这些小区域进行预测")

if __name__ == "__main__":
    main()
