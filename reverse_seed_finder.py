#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
逆向种子查找工具
根据开奖时间戳和开奖房间号码，逆向找出产生该结果的具体种子
"""

import csv
import re
import random
from typing import List, Dict, Tuple, Set
from collections import defaultdict

class ReverseSeedFinder:
    """逆向种子查找器"""
    
    def __init__(self):
        self.lottery_data = []
    
    def load_lottery_data(self, csv_file: str) -> bool:
        """加载开奖数据"""
        try:
            csv.field_size_limit(2000000)
            
            with open(csv_file, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.strip().split('\n')
                
                for i, line in enumerate(lines[1:], 1):
                    try:
                        fields = self._parse_csv_line(line)
                        
                        if len(fields) >= 7:
                            period = fields[1]
                            draw_timestamp = int(fields[2])
                            output_room = int(fields[6])
                            
                            record = {
                                'period': period,
                                'draw_timestamp_seconds': draw_timestamp,
                                'actual_output_room': output_room
                            }
                            
                            self.lottery_data.append(record)
                        
                    except Exception as e:
                        continue
            
            print(f"✅ 成功加载 {len(self.lottery_data)} 条开奖记录")
            return len(self.lottery_data) > 0
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return False
    
    def _parse_csv_line(self, line: str) -> List[str]:
        """解析CSV行"""
        fields = []
        current_field = ""
        in_quotes = False
        bracket_count = 0
        
        i = 0
        while i < len(line):
            char = line[i]
            
            if char == '"' and (i == 0 or line[i-1] != '\\'):
                in_quotes = not in_quotes
                current_field += char
            elif char == '[':
                bracket_count += 1
                current_field += char
            elif char == ']':
                bracket_count -= 1
                current_field += char
            elif char == ',' and not in_quotes and bracket_count == 0:
                fields.append(current_field.strip())
                current_field = ""
            else:
                current_field += char
            
            i += 1
        
        if current_field:
            fields.append(current_field.strip())
        
        return fields
    
    def calculate_first_seed(self, draw_timestamp_seconds: int) -> int:
        """计算首个种子"""
        return (draw_timestamp_seconds * 1000) - 5000
    
    def unity_random_algorithm(self, seed: int) -> int:
        """Unity Random算法"""
        random.seed(seed)
        return random.randint(1, 8)
    
    def find_matching_seeds(self, draw_timestamp_seconds: int, target_room: int) -> List[Dict]:
        """找出产生指定房间号的所有种子"""
        first_seed = self.calculate_first_seed(draw_timestamp_seconds)
        
        matching_seeds = []
        
        # 搜索9000个种子位置
        for position in range(9000):
            seed = first_seed + position
            generated_room = self.unity_random_algorithm(seed)
            
            if generated_room == target_room:
                matching_seeds.append({
                    'position': position,
                    'seed': seed,
                    'generated_room': generated_room
                })
        
        return matching_seeds
    
    def analyze_single_record(self, record: Dict) -> Dict:
        """分析单条开奖记录"""
        draw_timestamp = record['draw_timestamp_seconds']
        actual_room = record['actual_output_room']
        period = record['period']
        
        # 找出所有匹配的种子
        matching_seeds = self.find_matching_seeds(draw_timestamp, actual_room)
        
        analysis = {
            'period': period,
            'draw_timestamp': draw_timestamp,
            'actual_room': actual_room,
            'first_seed': self.calculate_first_seed(draw_timestamp),
            'matching_seeds': matching_seeds,
            'matching_count': len(matching_seeds),
            'matching_positions': [seed['position'] for seed in matching_seeds]
        }
        
        return analysis
    
    def analyze_all_records(self) -> Dict:
        """分析所有开奖记录"""
        print("🔍 分析所有开奖记录的种子匹配情况...")
        
        all_analyses = []
        position_frequency = defaultdict(int)  # 统计各位置被命中的频次
        room_position_mapping = defaultdict(list)  # 房间号到位置的映射
        
        for i, record in enumerate(self.lottery_data, 1):
            print(f"分析进度: {i}/{len(self.lottery_data)}", end='\r')
            
            analysis = self.analyze_single_record(record)
            all_analyses.append(analysis)
            
            # 统计位置频次
            for position in analysis['matching_positions']:
                position_frequency[position] += 1
                room_position_mapping[analysis['actual_room']].append(position)
        
        print()  # 换行
        
        # 分析位置分布
        position_stats = {
            'total_records': len(self.lottery_data),
            'position_frequency': dict(position_frequency),
            'room_position_mapping': dict(room_position_mapping),
            'most_frequent_positions': sorted(position_frequency.items(), 
                                            key=lambda x: x[1], reverse=True)[:20],
            'all_analyses': all_analyses
        }
        
        return position_stats
    
    def find_position_patterns(self, position_stats: Dict) -> Dict:
        """寻找位置模式"""
        print("🎯 寻找种子位置模式...")
        
        patterns = {
            'hot_positions': [],  # 热门位置
            'cold_positions': [],  # 冷门位置
            'room_preferences': {},  # 各房间的位置偏好
            'position_room_distribution': {}  # 各位置的房间分布
        }
        
        total_records = position_stats['total_records']
        position_frequency = position_stats['position_frequency']
        
        # 分析热门和冷门位置
        avg_frequency = sum(position_frequency.values()) / len(position_frequency) if position_frequency else 0
        
        for position, frequency in position_frequency.items():
            frequency_rate = frequency / total_records * 100
            
            if frequency > avg_frequency * 1.5:
                patterns['hot_positions'].append({
                    'position': position,
                    'frequency': frequency,
                    'rate': frequency_rate
                })
            elif frequency < avg_frequency * 0.5:
                patterns['cold_positions'].append({
                    'position': position,
                    'frequency': frequency,
                    'rate': frequency_rate
                })
        
        # 分析各房间的位置偏好
        room_position_mapping = position_stats['room_position_mapping']
        
        for room, positions in room_position_mapping.items():
            position_counts = defaultdict(int)
            for pos in positions:
                position_counts[pos] += 1
            
            # 找出该房间最常出现的位置
            most_common_positions = sorted(position_counts.items(), 
                                         key=lambda x: x[1], reverse=True)[:5]
            
            patterns['room_preferences'][room] = {
                'total_occurrences': len(positions),
                'unique_positions': len(set(positions)),
                'most_common_positions': most_common_positions,
                'position_distribution': dict(position_counts)
            }
        
        # 分析各位置的房间分布
        for position, frequency in position_frequency.items():
            # 统计该位置产生各房间的次数
            room_counts = defaultdict(int)
            
            for analysis in position_stats['all_analyses']:
                if position in analysis['matching_positions']:
                    room_counts[analysis['actual_room']] += 1
            
            patterns['position_room_distribution'][position] = {
                'total_hits': frequency,
                'room_distribution': dict(room_counts),
                'most_common_room': max(room_counts.items(), key=lambda x: x[1]) if room_counts else (0, 0)
            }
        
        return patterns
    
    def generate_reverse_analysis_report(self, position_stats: Dict, patterns: Dict) -> str:
        """生成逆向分析报告"""
        report = []
        report.append("🔍 逆向种子分析报告")
        report.append("=" * 70)
        report.append(f"📊 分析记录数: {position_stats['total_records']}")
        report.append(f"🎯 总命中位置数: {len(position_stats['position_frequency'])}")
        report.append("")
        
        # 最频繁的位置
        report.append("🔥 最频繁命中的种子位置 (前10个):")
        for i, (position, frequency) in enumerate(position_stats['most_frequent_positions'][:10], 1):
            rate = frequency / position_stats['total_records'] * 100
            report.append(f"   {i:2d}. 位置{position:4d}: {frequency:3d}次 ({rate:5.1f}%)")
        report.append("")
        
        # 各房间的位置偏好
        report.append("🎲 各房间的种子位置偏好:")
        for room in range(1, 9):
            if room in patterns['room_preferences']:
                pref = patterns['room_preferences'][room]
                most_common = pref['most_common_positions'][0] if pref['most_common_positions'] else (0, 0)
                report.append(f"   房间{room}: 最常在位置{most_common[0]} ({most_common[1]}次)")
        report.append("")
        
        # 热门位置分析
        if patterns['hot_positions']:
            report.append("🌟 热门种子位置 (命中频率高):")
            for hot_pos in patterns['hot_positions'][:5]:
                pos_dist = patterns['position_room_distribution'][hot_pos['position']]
                most_common_room = pos_dist['most_common_room']
                report.append(f"   位置{hot_pos['position']:4d}: {hot_pos['frequency']}次 "
                             f"({hot_pos['rate']:.1f}%) - 最常产生房间{most_common_room[0]}")
        report.append("")
        
        # 位置分布统计
        report.append("📈 种子位置分布统计:")
        all_positions = list(position_stats['position_frequency'].keys())
        if all_positions:
            min_pos = min(all_positions)
            max_pos = max(all_positions)
            avg_pos = sum(all_positions) / len(all_positions)
            
            report.append(f"   位置范围: {min_pos} - {max_pos}")
            report.append(f"   平均位置: {avg_pos:.1f}")
            report.append(f"   位置分布: 在9000个可能位置中，{len(all_positions)}个位置被命中")
        report.append("")
        
        # 实用建议
        report.append("💡 实用建议:")
        if patterns['hot_positions']:
            hot_positions = [str(pos['position']) for pos in patterns['hot_positions'][:3]]
            report.append(f"   🎯 关注热门位置: {', '.join(hot_positions)}")
        
        # 找出最稳定的预测位置
        stable_positions = []
        for pos, dist in patterns['position_room_distribution'].items():
            if dist['total_hits'] >= 3:  # 至少命中3次
                most_common_room = dist['most_common_room']
                if most_common_room[1] / dist['total_hits'] >= 0.6:  # 60%以上都是同一房间
                    stable_positions.append((pos, most_common_room[0], most_common_room[1], dist['total_hits']))
        
        if stable_positions:
            report.append("   🎲 稳定预测位置:")
            stable_positions.sort(key=lambda x: x[3], reverse=True)  # 按总命中次数排序
            for pos, room, room_hits, total_hits in stable_positions[:3]:
                consistency = room_hits / total_hits * 100
                report.append(f"     位置{pos}: {consistency:.1f}%概率产生房间{room} ({room_hits}/{total_hits})")
        
        report.append("=" * 70)
        
        return "\n".join(report)
    
    def find_predictive_positions(self, patterns: Dict) -> List[Dict]:
        """找出有预测价值的位置"""
        predictive_positions = []
        
        for position, distribution in patterns['position_room_distribution'].items():
            total_hits = distribution['total_hits']
            
            if total_hits >= 3:  # 至少命中3次才有统计意义
                most_common_room = distribution['most_common_room']
                consistency = most_common_room[1] / total_hits
                
                if consistency >= 0.5:  # 50%以上的一致性
                    predictive_positions.append({
                        'position': position,
                        'predicted_room': most_common_room[0],
                        'consistency': consistency * 100,
                        'hits': most_common_room[1],
                        'total_hits': total_hits,
                        'room_distribution': distribution['room_distribution']
                    })
        
        # 按一致性排序
        predictive_positions.sort(key=lambda x: x['consistency'], reverse=True)
        
        return predictive_positions

def main():
    """主函数"""
    print("🔍 逆向种子查找工具")
    print("=" * 60)
    print("根据开奖结果逆向找出产生该结果的具体种子位置")
    print()
    
    # 创建逆向查找器
    finder = ReverseSeedFinder()
    
    # 加载数据
    if not finder.load_lottery_data('real_time_data_20250817.csv'):
        return
    
    print()
    
    # 分析所有记录
    position_stats = finder.analyze_all_records()
    
    # 寻找位置模式
    patterns = finder.find_position_patterns(position_stats)
    
    # 生成报告
    report = finder.generate_reverse_analysis_report(position_stats, patterns)
    print(report)
    
    # 找出有预测价值的位置
    predictive_positions = finder.find_predictive_positions(patterns)
    
    if predictive_positions:
        print("\n🎯 发现有预测价值的种子位置:")
        print("-" * 50)
        
        for i, pos_info in enumerate(predictive_positions[:10], 1):
            print(f"{i:2d}. 位置{pos_info['position']:4d}: "
                  f"{pos_info['consistency']:5.1f}%概率产生房间{pos_info['predicted_room']} "
                  f"({pos_info['hits']}/{pos_info['total_hits']})")
    else:
        print("\n❌ 未发现明显的预测价值位置")
    
    # 保存报告
    with open('reverse_seed_analysis_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n💾 详细报告已保存到: reverse_seed_analysis_report.txt")
    
    # 演示单个记录的逆向分析
    if finder.lottery_data:
        print(f"\n🔍 演示：分析第一条记录")
        print("-" * 40)
        
        first_record = finder.lottery_data[0]
        analysis = finder.analyze_single_record(first_record)
        
        print(f"期号: {analysis['period']}")
        print(f"开奖时间戳: {analysis['draw_timestamp']}")
        print(f"开奖房间: {analysis['actual_room']}")
        print(f"首个种子: {analysis['first_seed']}")
        print(f"匹配的种子数量: {analysis['matching_count']}")
        print(f"匹配的位置: {analysis['matching_positions'][:10]}...")  # 只显示前10个

if __name__ == "__main__":
    main()
