#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试区域分析器模块

简单测试区域分析器的功能
"""

from region_analyzer import analyze_best_region

def test_single_region():
    """测试单个区域分析"""
    print("=== 测试单个区域分析 ===")
    
    # 测试区域大小为15
    print("测试区域大小为15:")
    result = analyze_best_region(15, verbose=True)
    print(f"返回结果: {result}")
    
    return result

if __name__ == "__main__":
    # 只测试单个区域分析
    best_range = test_single_region()
    
    if best_range:
        print(f"\n✅ 测试成功！最佳区域范围: {best_range}")
    else:
        print(f"\n❌ 测试失败！")
