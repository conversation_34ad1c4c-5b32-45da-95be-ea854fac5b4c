#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
区域分析器模块

提供简单的接口来分析滑动窗口区域的准确率，并返回最佳区域范围。

使用示例:
    from region_analyzer import analyze_best_region
    
    # 分析区域大小为15的最佳区域
    best_range = analyze_best_region(15)
    print(f"最佳区域: {best_range}")  # 输出: 6608:6622
    
    # 分析区域大小为10的最佳区域
    best_range = analyze_best_region(10)
    print(f"最佳区域: {best_range}")  # 输出: 1234:1243

作者: 预测分析系统
版本: 1.0
"""

from flexible_region_analysis import analyze_region_accuracy_flexible


def analyze_best_region(region_size=15, verbose=True):
    """
    分析指定区域大小下准确率最高的索引范围
    
    Args:
        region_size (int): 滑动窗口大小，默认为15个索引位置
        verbose (bool): 是否显示详细信息，默认为True
        
    Returns:
        str: 格式为 "start_index:end_index" 的字符串，如 "6608:6622"
        如果没有找到有效区域，返回 None
        
    Example:
        >>> best_range = analyze_best_region(15)
        >>> print(best_range)
        6608:6622
    """
    try:
        if verbose:
            print(f"🔍 开始分析区域大小为 {region_size} 的最佳区域...")
        
        # 分析指定区域大小的准确率
        region_accuracies = analyze_region_accuracy_flexible(region_size)
        
        if not region_accuracies:
            if verbose:
                print(f"❌ 没有找到有效的区域数据")
            return None
        
        # 获取准确率最高的区域（已经按准确率排序）
        best_region = region_accuracies[0]
        
        # 返回格式化的索引范围
        result = f"{best_region['start_index']}:{best_region['end_index']}"
        
        if verbose:
            print(f"✅ 最佳区域: {result}")
            print(f"   准确率: {best_region['accuracy']:.2f}%")
            print(f"   命中次数: {best_region['hit_count']}/{best_region['total_count']}")
            print(f"   全覆盖率: {best_region['full_coverage_rate']:.2f}%")
        
        return result
        
    except Exception as e:
        if verbose:
            print(f"❌ 分析过程中出现错误: {e}")
        return None


def analyze_multiple_sizes(sizes=[5, 10, 15, 20, 25], verbose=True):
    """
    分析多个区域大小，返回每个大小的最佳区域
    
    Args:
        sizes (list): 要分析的区域大小列表，默认为[5, 10, 15, 20, 25]
        verbose (bool): 是否显示详细信息，默认为True
        
    Returns:
        dict: 键为区域大小，值为最佳区域范围的字典
        
    Example:
        >>> results = analyze_multiple_sizes([10, 15, 20])
        >>> print(results)
        {10: '1234:1243', 15: '6608:6622', 20: '2345:2364'}
    """
    results = {}
    
    if verbose:
        print(f"🔍 开始分析多个区域大小: {sizes}")
        print("=" * 60)
    
    for size in sizes:
        if verbose:
            print(f"\n📊 分析区域大小: {size}")
            print("-" * 40)
        
        best_range = analyze_best_region(size, verbose=verbose)
        results[size] = best_range
        
        if verbose and best_range:
            print(f"区域大小 {size} 的最佳范围: {best_range}")
    
    if verbose:
        print("\n" + "=" * 60)
        print("📋 汇总结果:")
        for size, range_str in results.items():
            if range_str:
                print(f"  区域大小 {size:2d}: {range_str}")
            else:
                print(f"  区域大小 {size:2d}: 无有效结果")
    
    return results


def get_region_details(region_size=15, top_n=5, verbose=True):
    """
    获取指定区域大小的前N个最佳区域详细信息
    
    Args:
        region_size (int): 滑动窗口大小，默认为15
        top_n (int): 返回前N个最佳区域，默认为5
        verbose (bool): 是否显示详细信息，默认为True
        
    Returns:
        list: 包含区域详细信息的字典列表
        
    Example:
        >>> details = get_region_details(15, 3)
        >>> for detail in details:
        ...     print(f"{detail['range']}: {detail['accuracy']:.2f}%")
    """
    try:
        if verbose:
            print(f"🔍 获取区域大小为 {region_size} 的前 {top_n} 个最佳区域...")
        
        # 分析指定区域大小的准确率
        region_accuracies = analyze_region_accuracy_flexible(region_size)
        
        if not region_accuracies:
            if verbose:
                print(f"❌ 没有找到有效的区域数据")
            return []
        
        # 获取前N个最佳区域
        top_regions = region_accuracies[:top_n]
        
        results = []
        for i, region in enumerate(top_regions, 1):
            region_info = {
                'rank': i,
                'range': f"{region['start_index']}:{region['end_index']}",
                'start_index': region['start_index'],
                'end_index': region['end_index'],
                'accuracy': region['accuracy'],
                'hit_count': region['hit_count'],
                'total_count': region['total_count'],
                'full_coverage_rate': region['full_coverage_rate']
            }
            results.append(region_info)
            
            if verbose:
                print(f"  {i}. {region_info['range']} - 准确率: {region_info['accuracy']:.2f}%")
        
        return results
        
    except Exception as e:
        if verbose:
            print(f"❌ 获取区域详情时出现错误: {e}")
        return []


if __name__ == "__main__":
    # 示例用法
    print("=== 区域分析器示例 ===")
    
    # 分析单个区域大小
    print("\n1. 分析区域大小为15的最佳区域:")
    best_range = analyze_best_region(15)
    print(f"结果: {best_range}")
    
    # 分析多个区域大小
    print("\n2. 分析多个区域大小:")
    results = analyze_multiple_sizes([10, 15, 20], verbose=False)
    for size, range_str in results.items():
        print(f"区域大小 {size}: {range_str}")
    
    # 获取详细信息
    print("\n3. 获取前3个最佳区域详情:")
    details = get_region_details(15, 3, verbose=False)
    for detail in details:
        print(f"排名 {detail['rank']}: {detail['range']} (准确率: {detail['accuracy']:.2f}%)")
