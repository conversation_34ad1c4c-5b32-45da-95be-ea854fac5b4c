#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版种子算法逆向工程工具
专门处理100%准确率区域的种子分析
"""

import csv
import random
import re
from typing import List, Dict, Tuple
from collections import defaultdict

class SimpleReverseEngineer:
    """简化版种子逆向工程器"""
    
    def __init__(self):
        # 100%准确率的滑动窗口区域（来自region_size_38_accuracy_report.txt）
        self.perfect_regions = [
            {'start': 2258, 'end': 2295, 'size': 38},  # 区域2258-2295
            {'start': 2259, 'end': 2296, 'size': 38},  # 区域2259-2296
            {'start': 4344, 'end': 4381, 'size': 38},  # 区域4344-4381
            {'start': 4345, 'end': 4382, 'size': 38},  # 区域4345-4382
            {'start': 4346, 'end': 4383, 'size': 38},  # 区域4346-4383
        ]
        
        self.lottery_data = []
    
    def load_lottery_data_simple(self, csv_file: str) -> bool:
        """
        简化加载开奖数据
        
        Args:
            csv_file: CSV文件路径
            
        Returns:
            是否加载成功
        """
        try:
            csv.field_size_limit(2000000)  # 增加字段大小限制
            
            with open(csv_file, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.strip().split('\n')
                
                if len(lines) < 2:
                    print("❌ CSV文件格式错误")
                    return False
                
                header = lines[0].split(',')
                print(f"📋 CSV头部: {header}")
                
                for i, line in enumerate(lines[1:], 1):
                    try:
                        # 手动解析CSV行，处理包含逗号的字段
                        fields = self._parse_csv_line(line)
                        
                        if len(fields) >= 7:
                            # 提取基本信息
                            period = fields[1]
                            draw_timestamp = int(fields[2])
                            output_room = int(fields[6])
                            
                            # 解析时间戳列表
                            timestamp_str = fields[3]
                            timestamp_list = self._parse_list_field(timestamp_str)
                            
                            # 解析随机数序列
                            random_str = fields[4]
                            random_sequence = self._parse_list_field(random_str)
                            
                            if timestamp_list and random_sequence:
                                lottery_record = {
                                    'period': period,
                                    'draw_timestamp': draw_timestamp,
                                    'timestamp_list': timestamp_list,
                                    'random_sequence': random_sequence,
                                    'output_room': output_room
                                }
                                
                                self.lottery_data.append(lottery_record)
                        
                    except Exception as e:
                        print(f"⚠️ 跳过第{i}行，解析错误: {e}")
                        continue
            
            print(f"✅ 成功加载 {len(self.lottery_data)} 条开奖记录")
            return len(self.lottery_data) > 0
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return False
    
    def _parse_csv_line(self, line: str) -> List[str]:
        """解析CSV行，处理包含逗号的字段"""
        fields = []
        current_field = ""
        in_quotes = False
        bracket_count = 0
        
        i = 0
        while i < len(line):
            char = line[i]
            
            if char == '"' and (i == 0 or line[i-1] != '\\'):
                in_quotes = not in_quotes
                current_field += char
            elif char == '[':
                bracket_count += 1
                current_field += char
            elif char == ']':
                bracket_count -= 1
                current_field += char
            elif char == ',' and not in_quotes and bracket_count == 0:
                fields.append(current_field.strip())
                current_field = ""
            else:
                current_field += char
            
            i += 1
        
        if current_field:
            fields.append(current_field.strip())
        
        return fields
    
    def _parse_list_field(self, field_str: str) -> List[int]:
        """解析列表字段"""
        try:
            # 移除引号
            field_str = field_str.strip('"')
            
            # 使用正则表达式提取数字
            numbers = re.findall(r'\d+', field_str)
            return [int(num) for num in numbers]
            
        except Exception as e:
            print(f"⚠️ 解析列表字段失败: {e}")
            return []
    
    def analyze_perfect_region_seeds(self, region_index: int = 0) -> Dict:
        """
        分析指定100%准确率区域的种子模式
        
        Args:
            region_index: 区域索引（0-4）
            
        Returns:
            分析结果字典
        """
        if region_index >= len(self.perfect_regions):
            print(f"❌ 区域索引超出范围: {region_index}")
            return {}
        
        region = self.perfect_regions[region_index]
        region_key = f"{region['start']}-{region['end']}"
        
        print(f"\n🔍 分析100%准确率区域 {region_key}...")
        
        analysis_result = {
            'region': region,
            'seed_to_random_mappings': {},
            'seed_to_output_mappings': {},
            'algorithm_test_results': {},
            'pattern_analysis': {}
        }
        
        # 收集该区域的种子和随机数映射
        for record in self.lottery_data:
            timestamp_list = record['timestamp_list']
            random_sequence = record['random_sequence']
            output_room = record['output_room']
            
            # 提取该区域的种子和对应的随机数
            for i in range(region['start'], region['end'] + 1):
                if i < len(timestamp_list) and i < len(random_sequence):
                    seed = timestamp_list[i]
                    random_num = random_sequence[i]
                    
                    # 记录种子到随机数的映射
                    if seed not in analysis_result['seed_to_random_mappings']:
                        analysis_result['seed_to_random_mappings'][seed] = []
                    analysis_result['seed_to_random_mappings'][seed].append(random_num)
                    
                    # 记录种子到输出房间的映射
                    if seed not in analysis_result['seed_to_output_mappings']:
                        analysis_result['seed_to_output_mappings'][seed] = []
                    analysis_result['seed_to_output_mappings'][seed].append(output_room)
        
        # 分析一致性映射
        consistent_mappings = {}
        for seed, randoms in analysis_result['seed_to_random_mappings'].items():
            unique_randoms = list(set(randoms))
            if len(unique_randoms) == 1:  # 同一种子总是产生相同随机数
                consistent_mappings[seed] = unique_randoms[0]
        
        print(f"📊 发现 {len(consistent_mappings)} 个一致性种子映射")
        
        # 测试各种算法
        algorithm_results = self._test_algorithms(consistent_mappings)
        analysis_result['algorithm_test_results'] = algorithm_results
        
        # 分析模式
        pattern_results = self._analyze_patterns(consistent_mappings)
        analysis_result['pattern_analysis'] = pattern_results
        
        return analysis_result
    
    def _test_algorithms(self, seed_mappings: Dict[int, int]) -> Dict:
        """测试各种可能的算法"""
        algorithms = {
            'unity_python_random': self._test_unity_random,
            'modulo_8_plus_1': lambda seed, expected: ((seed % 8) + 1) == expected,
            'modulo_9_plus_1': lambda seed, expected: ((seed % 9) + 1) == expected,
            'hash_mod_8_plus_1': lambda seed, expected: ((hash(seed) % 8) + 1) == expected,
            'last_digit_mod_8_plus_1': lambda seed, expected: ((seed % 10 % 8) + 1) == expected,
        }
        
        results = {}
        
        for algo_name, algo_func in algorithms.items():
            matches = 0
            total = len(seed_mappings)
            
            for seed, expected in seed_mappings.items():
                try:
                    if algo_func(seed, expected):
                        matches += 1
                except:
                    pass
            
            accuracy = (matches / total * 100) if total > 0 else 0
            results[algo_name] = {
                'matches': matches,
                'total': total,
                'accuracy': accuracy
            }
        
        return results
    
    def _test_unity_random(self, seed: int, expected: int) -> bool:
        """测试Unity Random算法"""
        random.seed(seed)
        result = random.randint(1, 8)
        return result == expected
    
    def _analyze_patterns(self, seed_mappings: Dict[int, int]) -> Dict:
        """分析种子模式"""
        patterns = {
            'seed_distribution': {},
            'random_distribution': {},
            'seed_ranges': {},
            'correlation_analysis': {}
        }
        
        # 分析随机数分布
        random_counts = defaultdict(int)
        for random_num in seed_mappings.values():
            random_counts[random_num] += 1
        
        patterns['random_distribution'] = dict(random_counts)
        
        # 分析种子范围
        if seed_mappings:
            seeds = list(seed_mappings.keys())
            patterns['seed_ranges'] = {
                'min_seed': min(seeds),
                'max_seed': max(seeds),
                'seed_count': len(seeds),
                'seed_span': max(seeds) - min(seeds)
            }
        
        return patterns
    
    def generate_simple_report(self, analysis_result: Dict) -> str:
        """生成简化报告"""
        if not analysis_result:
            return "❌ 无分析结果"
        
        region = analysis_result['region']
        region_key = f"{region['start']}-{region['end']}"
        
        report = []
        report.append("=" * 60)
        report.append(f"🔬 区域 {region_key} 种子算法逆向分析报告")
        report.append("=" * 60)
        
        # 基本统计
        seed_count = len(analysis_result['seed_to_random_mappings'])
        consistent_count = len([s for s, r in analysis_result['seed_to_random_mappings'].items() 
                               if len(set(r)) == 1])
        
        report.append(f"📊 基本统计:")
        report.append(f"   总种子数: {seed_count}")
        report.append(f"   一致性映射: {consistent_count}")
        report.append(f"   一致性比例: {consistent_count/seed_count*100:.1f}%" if seed_count > 0 else "   一致性比例: 0%")
        report.append("")
        
        # 算法测试结果
        algo_results = analysis_result['algorithm_test_results']
        if algo_results:
            report.append("🧪 算法测试结果:")
            sorted_algos = sorted(algo_results.items(), key=lambda x: x[1]['accuracy'], reverse=True)
            
            for algo_name, result in sorted_algos:
                report.append(f"   {algo_name}: {result['accuracy']:.2f}% ({result['matches']}/{result['total']})")
            
            # 找出最佳算法
            best_algo = sorted_algos[0]
            if best_algo[1]['accuracy'] >= 95:
                report.append(f"\n🏆 发现高匹配算法: {best_algo[0]} (准确率: {best_algo[1]['accuracy']:.2f}%)")
            elif best_algo[1]['accuracy'] >= 80:
                report.append(f"\n⚠️ 发现部分匹配算法: {best_algo[0]} (准确率: {best_algo[1]['accuracy']:.2f}%)")
            else:
                report.append(f"\n❌ 未发现明显匹配的算法模式")
        
        report.append("")
        
        # 模式分析
        patterns = analysis_result['pattern_analysis']
        if patterns:
            report.append("📈 模式分析:")
            
            if 'random_distribution' in patterns:
                report.append("   随机数分布:")
                for num, count in sorted(patterns['random_distribution'].items()):
                    report.append(f"     房间{num}: {count}次")
            
            if 'seed_ranges' in patterns:
                ranges = patterns['seed_ranges']
                report.append(f"   种子范围: {ranges.get('min_seed', 'N/A')} - {ranges.get('max_seed', 'N/A')}")
                report.append(f"   种子跨度: {ranges.get('seed_span', 'N/A')}")
        
        report.append("=" * 60)
        
        return "\n".join(report)
    
    def analyze_all_perfect_regions(self) -> Dict:
        """分析所有100%准确率区域"""
        all_results = {}
        
        for i, region in enumerate(self.perfect_regions):
            region_key = f"{region['start']}-{region['end']}"
            print(f"\n🎯 分析区域 {i+1}/{len(self.perfect_regions)}: {region_key}")
            
            result = self.analyze_perfect_region_seeds(i)
            all_results[region_key] = result
            
            # 生成并显示报告
            report = self.generate_simple_report(result)
            print(report)
        
        return all_results

def main():
    """主函数"""
    print("🔬 简化版种子算法逆向工程工具")
    print("=" * 50)
    
    # 创建逆向工程器
    engineer = SimpleReverseEngineer()
    
    # 加载数据
    if not engineer.load_lottery_data_simple('real_time_data_20250817.csv'):
        print("❌ 数据加载失败，程序退出")
        return
    
    # 分析所有100%准确率区域
    print("\n🎯 开始分析所有100%准确率区域...")
    all_results = engineer.analyze_all_perfect_regions()
    
    # 生成总结报告
    print("\n📋 总结报告:")
    print("=" * 50)
    
    best_algorithms = {}
    for region_key, result in all_results.items():
        if result and 'algorithm_test_results' in result:
            algo_results = result['algorithm_test_results']
            if algo_results:
                best_algo = max(algo_results.items(), key=lambda x: x[1]['accuracy'])
                best_algorithms[region_key] = best_algo
                print(f"区域 {region_key}: {best_algo[0]} (准确率: {best_algo[1]['accuracy']:.2f}%)")
    
    print("\n✅ 逆向工程分析完成！")

if __name__ == "__main__":
    main()
