#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
种子计算规律分析工具
分析开奖时间戳与随机数序列时间戳的关系
"""

import csv
import re
from typing import List, Dict, Tuple

class SeedAnalyzer:
    """种子计算规律分析器"""
    
    def __init__(self):
        self.lottery_data = []
    
    def load_data(self, csv_file: str) -> bool:
        """加载开奖数据"""
        try:
            csv.field_size_limit(2000000)
            
            with open(csv_file, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.strip().split('\n')
                
                for i, line in enumerate(lines[1:], 1):
                    try:
                        fields = self._parse_csv_line(line)
                        
                        if len(fields) >= 7:
                            period = fields[1]
                            draw_timestamp = int(fields[2])
                            output_room = int(fields[6])
                            
                            # 解析时间戳列表
                            timestamp_str = fields[3]
                            timestamp_list = self._parse_list_field(timestamp_str)
                            
                            if timestamp_list and len(timestamp_list) > 0:
                                record = {
                                    'period': period,
                                    'draw_timestamp': draw_timestamp,
                                    'timestamp_list': timestamp_list,
                                    'output_room': output_room,
                                    'first_timestamp': timestamp_list[0],
                                    'last_timestamp': timestamp_list[-1],
                                    'timestamp_count': len(timestamp_list)
                                }
                                
                                self.lottery_data.append(record)
                        
                    except Exception as e:
                        continue
            
            print(f"✅ 成功加载 {len(self.lottery_data)} 条记录")
            return len(self.lottery_data) > 0
            
        except Exception as e:
            print(f"❌ 加载失败: {e}")
            return False
    
    def _parse_csv_line(self, line: str) -> List[str]:
        """解析CSV行"""
        fields = []
        current_field = ""
        in_quotes = False
        bracket_count = 0
        
        i = 0
        while i < len(line):
            char = line[i]
            
            if char == '"' and (i == 0 or line[i-1] != '\\'):
                in_quotes = not in_quotes
                current_field += char
            elif char == '[':
                bracket_count += 1
                current_field += char
            elif char == ']':
                bracket_count -= 1
                current_field += char
            elif char == ',' and not in_quotes and bracket_count == 0:
                fields.append(current_field.strip())
                current_field = ""
            else:
                current_field += char
            
            i += 1
        
        if current_field:
            fields.append(current_field.strip())
        
        return fields
    
    def _parse_list_field(self, field_str: str) -> List[int]:
        """解析列表字段"""
        try:
            field_str = field_str.strip('"')
            numbers = re.findall(r'\d+', field_str)
            return [int(num) for num in numbers]
        except:
            return []
    
    def analyze_seed_patterns(self) -> Dict:
        """分析种子计算模式"""
        print("\n🔍 分析种子计算模式...")
        
        patterns = {
            'timestamp_relationships': [],
            'offset_patterns': [],
            'sequence_patterns': [],
            'statistics': {}
        }
        
        for record in self.lottery_data:
            draw_ts = record['draw_timestamp']
            first_ts = record['first_timestamp']
            last_ts = record['last_timestamp']
            ts_count = record['timestamp_count']
            
            # 分析开奖时间戳与序列的关系
            offset_to_first = first_ts - draw_ts
            offset_to_last = last_ts - draw_ts
            
            relationship = {
                'period': record['period'],
                'draw_timestamp': draw_ts,
                'first_timestamp': first_ts,
                'last_timestamp': last_ts,
                'offset_to_first': offset_to_first,
                'offset_to_last': offset_to_last,
                'sequence_length': ts_count,
                'output_room': record['output_room']
            }
            
            patterns['timestamp_relationships'].append(relationship)
        
        # 统计偏移模式
        offsets_to_first = [r['offset_to_first'] for r in patterns['timestamp_relationships']]
        offsets_to_last = [r['offset_to_last'] for r in patterns['timestamp_relationships']]
        sequence_lengths = [r['sequence_length'] for r in patterns['timestamp_relationships']]
        
        patterns['statistics'] = {
            'offset_to_first': {
                'min': min(offsets_to_first),
                'max': max(offsets_to_first),
                'unique_values': list(set(offsets_to_first)),
                'most_common': max(set(offsets_to_first), key=offsets_to_first.count)
            },
            'offset_to_last': {
                'min': min(offsets_to_last),
                'max': max(offsets_to_last),
                'unique_values': list(set(offsets_to_last)),
                'most_common': max(set(offsets_to_last), key=offsets_to_last.count)
            },
            'sequence_length': {
                'min': min(sequence_lengths),
                'max': max(sequence_lengths),
                'unique_values': list(set(sequence_lengths)),
                'most_common': max(set(sequence_lengths), key=sequence_lengths.count)
            }
        }
        
        return patterns
    
    def find_seed_calculation_rule(self) -> Dict:
        """寻找种子计算规则"""
        print("\n🎯 寻找种子计算规则...")

        if not self.lottery_data:
            return {}

        # 分析第一条记录的详细模式
        sample_record = self.lottery_data[0]
        draw_ts = sample_record['draw_timestamp']
        ts_list = sample_record['timestamp_list']

        print(f"📊 样本分析 (期号: {sample_record['period']}):")
        print(f"   开奖时间戳: {draw_ts}")
        print(f"   序列首个时间戳: {ts_list[0]}")
        print(f"   序列最后时间戳: {ts_list[-1]}")
        print(f"   序列长度: {len(ts_list)}")

        # 分析时间戳序列的规律
        if len(ts_list) >= 2:
            intervals = []
            for i in range(1, min(10, len(ts_list))):  # 检查前10个间隔
                interval = ts_list[i] - ts_list[i-1]
                intervals.append(interval)

            print(f"   前几个时间戳间隔: {intervals}")

            # 检查是否是连续递增
            is_consecutive = all(interval == 1 for interval in intervals)
            print(f"   是否连续递增: {is_consecutive}")

        # 分析基础时间戳的计算规则
        base_offset = ts_list[0] - draw_ts
        print(f"   基础偏移量: {base_offset}")

        # 检查所有记录的基础偏移量和时间戳关系
        print("\n🔍 详细分析前10条记录:")
        offset_analysis = []
        for i, record in enumerate(self.lottery_data[:10]):
            draw_ts = record['draw_timestamp']
            first_ts = record['first_timestamp']
            offset = first_ts - draw_ts

            # 尝试找出基础时间戳的规律
            # 检查是否是基于某个基准时间的计算
            base_time_candidates = []

            # 候选1: 去掉毫秒部分，向下取整到秒
            base_candidate_1 = (draw_ts // 1000) * 1000
            offset_1 = first_ts - base_candidate_1

            # 候选2: 向前偏移几秒
            for seconds_back in [1, 2, 3, 4, 5]:
                base_candidate = draw_ts - (seconds_back * 1000)
                offset_candidate = first_ts - base_candidate
                base_time_candidates.append((seconds_back, base_candidate, offset_candidate))

            analysis_item = {
                'period': record['period'],
                'draw_timestamp': draw_ts,
                'first_timestamp': first_ts,
                'raw_offset': offset,
                'base_candidate_1': base_candidate_1,
                'offset_1': offset_1,
                'candidates': base_time_candidates
            }

            offset_analysis.append(analysis_item)

            print(f"   记录{i+1} (期号{record['period']}):")
            print(f"     开奖时间戳: {draw_ts}")
            print(f"     首个种子: {first_ts}")
            print(f"     原始偏移: {offset}")
            print(f"     去毫秒偏移: {offset_1}")

        # 分析偏移量模式
        print(f"\n🔍 分析偏移量模式:")
        all_raw_offsets = [item['raw_offset'] for item in offset_analysis]
        all_offset_1 = [item['offset_1'] for item in offset_analysis]

        print(f"   原始偏移量: {all_raw_offsets}")
        print(f"   去毫秒偏移量: {all_offset_1}")

        # 检查去毫秒偏移量是否有规律
        unique_offset_1 = list(set(all_offset_1))
        print(f"   去毫秒偏移量唯一值: {unique_offset_1}")

        if len(unique_offset_1) == 1:
            print(f"   🎯 发现固定的去毫秒偏移量: {unique_offset_1[0]}")
            rule_type = "fixed_offset_no_ms"
            rule_value = unique_offset_1[0]
        elif len(unique_offset_1) <= 3:
            print(f"   🎯 发现少量去毫秒偏移量: {unique_offset_1}")
            rule_type = "limited_offset_no_ms"
            rule_value = unique_offset_1
        else:
            print(f"   ⚠️ 偏移量仍然不固定")
            rule_type = "variable_offset"
            rule_value = unique_offset_1

        return {
            'rule_type': rule_type,
            'rule_value': rule_value,
            'offset_analysis': offset_analysis,
            'sample_analysis': {
                'draw_timestamp': draw_ts,
                'first_sequence_timestamp': ts_list[0],
                'base_offset': base_offset,
                'sequence_length': len(ts_list),
                'is_consecutive': is_consecutive if len(ts_list) >= 2 else None
            }
        }
    
    def generate_seed_calculation_formula(self, rule_analysis: Dict) -> str:
        """生成种子计算公式"""
        if not rule_analysis:
            return "无法确定种子计算公式"
        
        rule_type = rule_analysis['rule_type']
        rule_value = rule_analysis['rule_value']
        sample = rule_analysis['sample_analysis']
        
        formula = []
        formula.append("🧮 种子计算公式:")
        formula.append("=" * 40)
        
        if rule_type == "fixed_offset":
            formula.append(f"基础种子 = 开奖时间戳 + {rule_value}")
            formula.append(f"种子序列 = [基础种子 + i for i in range(9000)]")
            formula.append("")
            formula.append("Python实现:")
            formula.append("```python")
            formula.append("def calculate_seeds(draw_timestamp):")
            formula.append(f"    base_seed = draw_timestamp + {rule_value}")
            formula.append("    return [base_seed + i for i in range(9000)]")
            formula.append("```")
            formula.append("")
            formula.append("示例:")
            formula.append(f"开奖时间戳: {sample['draw_timestamp']}")
            formula.append(f"基础种子: {sample['draw_timestamp']} + {rule_value} = {sample['first_sequence_timestamp']}")
        else:
            formula.append("⚠️ 偏移量不固定，可能的规律:")
            for offset in rule_value:
                formula.append(f"   - 偏移量: {offset}")
        
        return "\n".join(formula)
    
    def generate_report(self) -> str:
        """生成完整分析报告"""
        patterns = self.analyze_seed_patterns()
        rule_analysis = self.find_seed_calculation_rule()
        formula = self.generate_seed_calculation_formula(rule_analysis)
        
        report = []
        report.append("🔬 种子计算规律分析报告")
        report.append("=" * 60)
        report.append(f"📊 分析记录数: {len(self.lottery_data)}")
        report.append("")
        
        # 统计信息
        stats = patterns['statistics']
        report.append("📈 统计信息:")
        report.append(f"   序列长度: {stats['sequence_length']['most_common']} (最常见)")
        report.append(f"   基础偏移量: {stats['offset_to_first']['most_common']} (最常见)")
        report.append(f"   偏移量范围: {stats['offset_to_first']['min']} ~ {stats['offset_to_first']['max']}")
        report.append("")
        
        # 种子计算公式
        report.append(formula)
        report.append("")
        
        # 验证建议
        report.append("✅ 验证建议:")
        report.append("1. 使用公式计算种子序列")
        report.append("2. 应用Unity Random算法: random.seed(seed); random.randint(1,8)")
        report.append("3. 对比计算结果与实际开奖结果")
        report.append("4. 验证100%准确率区域的预测效果")
        
        return "\n".join(report)

def main():
    """主函数"""
    print("🔬 种子计算规律分析工具")
    print("=" * 50)
    
    analyzer = SeedAnalyzer()
    
    if not analyzer.load_data('real_time_data_20250817.csv'):
        return
    
    report = analyzer.generate_report()
    print(report)
    
    # 保存报告
    with open('seed_calculation_analysis.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n💾 分析报告已保存到: seed_calculation_analysis.txt")

if __name__ == "__main__":
    main()
