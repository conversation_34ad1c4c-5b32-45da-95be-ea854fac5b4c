#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
种子模式发现工具
专门分析种子计算的具体规律
"""

import csv
import re
from typing import List, Dict

def parse_csv_line(line: str) -> List[str]:
    """解析CSV行"""
    fields = []
    current_field = ""
    in_quotes = False
    bracket_count = 0
    
    i = 0
    while i < len(line):
        char = line[i]
        
        if char == '"' and (i == 0 or line[i-1] != '\\'):
            in_quotes = not in_quotes
            current_field += char
        elif char == '[':
            bracket_count += 1
            current_field += char
        elif char == ']':
            bracket_count -= 1
            current_field += char
        elif char == ',' and not in_quotes and bracket_count == 0:
            fields.append(current_field.strip())
            current_field = ""
        else:
            current_field += char
        
        i += 1
    
    if current_field:
        fields.append(current_field.strip())
    
    return fields

def parse_list_field(field_str: str) -> List[int]:
    """解析列表字段"""
    try:
        field_str = field_str.strip('"')
        numbers = re.findall(r'\d+', field_str)
        return [int(num) for num in numbers]
    except:
        return []

def analyze_seed_pattern():
    """分析种子模式"""
    print("🔍 种子模式深度分析")
    print("=" * 50)
    
    # 加载数据
    csv.field_size_limit(2000000)
    
    records = []
    with open('real_time_data_20250817.csv', 'r', encoding='utf-8') as f:
        content = f.read()
        lines = content.strip().split('\n')
        
        for i, line in enumerate(lines[1:6], 1):  # 只分析前5条记录
            try:
                fields = parse_csv_line(line)
                
                if len(fields) >= 7:
                    period = fields[1]
                    draw_timestamp = int(fields[2])
                    output_room = int(fields[6])
                    
                    timestamp_str = fields[3]
                    timestamp_list = parse_list_field(timestamp_str)
                    
                    if timestamp_list:
                        records.append({
                            'period': period,
                            'draw_timestamp': draw_timestamp,
                            'first_seed': timestamp_list[0],
                            'output_room': output_room,
                            'timestamp_list': timestamp_list[:10]  # 只取前10个
                        })
                        
            except Exception as e:
                print(f"跳过第{i}行: {e}")
                continue
    
    print(f"✅ 加载了 {len(records)} 条记录\n")
    
    # 详细分析每条记录
    for i, record in enumerate(records, 1):
        print(f"📊 记录 {i} (期号: {record['period']}):")
        print(f"   开奖时间戳: {record['draw_timestamp']}")
        print(f"   首个种子: {record['first_seed']}")
        print(f"   开奖房间: {record['output_room']}")
        
        # 分析时间戳关系
        draw_ts = record['draw_timestamp']
        first_seed = record['first_seed']
        
        # 计算各种可能的基础时间戳
        print(f"   时间戳分析:")
        
        # 方法1: 去掉毫秒，向前偏移
        draw_ts_no_ms = (draw_ts // 1000) * 1000
        offset_from_no_ms = first_seed - draw_ts_no_ms
        print(f"     去毫秒时间戳: {draw_ts_no_ms}")
        print(f"     从去毫秒偏移: {offset_from_no_ms}")
        
        # 方法2: 向前偏移固定秒数
        for seconds in [1, 2, 3, 4, 5, 6]:
            base_ts = draw_ts - (seconds * 1000)
            base_ts_no_ms = (base_ts // 1000) * 1000
            if base_ts_no_ms == first_seed:
                print(f"     🎯 匹配! 向前{seconds}秒并去毫秒: {base_ts} -> {base_ts_no_ms}")

        # 方法3: 检查是否是向前偏移到整秒
        seconds_forward = (draw_ts - first_seed) / 1000
        print(f"     向前偏移秒数: {seconds_forward:.3f}")

        # 方法4: 检查具体的偏移模式
        # 首个种子看起来是: 开奖时间戳向前偏移几秒，然后去掉毫秒
        if first_seed % 1000 == 0:  # 确实去掉了毫秒
            # 计算向前偏移了多少秒
            actual_seconds_forward = (draw_ts - first_seed) / 1000
            print(f"     实际向前偏移: {actual_seconds_forward:.3f}秒")

            # 检查是否接近整数秒
            rounded_seconds = round(actual_seconds_forward)
            if abs(actual_seconds_forward - rounded_seconds) < 0.1:
                print(f"     接近整数秒偏移: {rounded_seconds}秒")

                # 验证公式
                test_base = draw_ts - (rounded_seconds * 1000)
                test_base_no_ms = (test_base // 1000) * 1000
                if test_base_no_ms == first_seed:
                    print(f"     ✅ 公式验证成功: 向前{rounded_seconds}秒并去毫秒")
        
        # 显示种子序列的前几个
        print(f"   种子序列前10个: {record['timestamp_list']}")
        
        # 验证连续性
        if len(record['timestamp_list']) >= 2:
            intervals = [record['timestamp_list'][j+1] - record['timestamp_list'][j] 
                        for j in range(len(record['timestamp_list'])-1)]
            print(f"   种子间隔: {intervals}")
        
        print()
    
    # 寻找通用规律
    print("🎯 寻找通用规律:")
    print("-" * 30)
    
    # 检查所有记录的偏移模式
    patterns = []
    for record in records:
        draw_ts = record['draw_timestamp']
        first_seed = record['first_seed']

        # 计算向前偏移的秒数
        seconds_forward = (draw_ts - first_seed) / 1000

        # 检查是否接近整数秒偏移
        rounded_seconds = round(seconds_forward)
        if abs(seconds_forward - rounded_seconds) < 0.1:  # 允许0.1秒的误差

            # 验证: 开奖时间戳 - 偏移秒数 * 1000，然后去毫秒
            calculated_base = draw_ts - (rounded_seconds * 1000)
            calculated_base_no_ms = (calculated_base // 1000) * 1000

            if calculated_base_no_ms == first_seed:
                patterns.append({
                    'period': record['period'],
                    'seconds_forward': rounded_seconds,
                    'formula_works': True
                })
                print(f"   期号{record['period']}: 向前{rounded_seconds}秒 ✅")
            else:
                patterns.append({
                    'period': record['period'],
                    'seconds_forward': rounded_seconds,
                    'formula_works': False
                })
                print(f"   期号{record['period']}: 向前{rounded_seconds}秒 ❌")
        else:
            print(f"   期号{record['period']}: 非整数秒偏移 {seconds_forward:.3f}")
            # 尝试其他可能的偏移
            for test_seconds in [3, 4, 5, 6, 7, 8]:
                test_base = draw_ts - (test_seconds * 1000)
                test_base_no_ms = (test_base // 1000) * 1000
                if test_base_no_ms == first_seed:
                    patterns.append({
                        'period': record['period'],
                        'seconds_forward': test_seconds,
                        'formula_works': True
                    })
                    print(f"   期号{record['period']}: 找到匹配! 向前{test_seconds}秒 ✅")
                    break
    
    # 分析偏移秒数的分布
    working_patterns = [p for p in patterns if p['formula_works']]
    if working_patterns:
        seconds_forward_values = [p['seconds_forward'] for p in working_patterns]
        unique_seconds = list(set(seconds_forward_values))

        print(f"\n📈 有效偏移秒数: {seconds_forward_values}")
        print(f"📈 唯一偏移秒数: {unique_seconds}")

        if len(unique_seconds) == 1:
            print(f"🎉 发现固定偏移规律: 向前 {unique_seconds[0]} 秒")
            formula = f"""
🧮 种子计算公式:
===============
def calculate_first_seed(draw_timestamp):
    # 向前偏移 {unique_seconds[0]} 秒
    base_timestamp = draw_timestamp - ({unique_seconds[0]} * 1000)
    # 去掉毫秒部分
    first_seed = (base_timestamp // 1000) * 1000
    return first_seed

def calculate_seed_sequence(draw_timestamp):
    first_seed = calculate_first_seed(draw_timestamp)
    return [first_seed + i for i in range(9000)]
"""
            print(formula)
        else:
            print(f"⚠️ 偏移秒数不固定，可能的值: {unique_seconds}")

    # 验证公式
    if working_patterns and len(set(p['seconds_forward'] for p in working_patterns)) == 1:
        offset_seconds = working_patterns[0]['seconds_forward']
        print(f"\n✅ 验证公式 (向前偏移{offset_seconds}秒):")

        for record in records:
            draw_ts = record['draw_timestamp']
            actual_first_seed = record['first_seed']

            # 应用公式
            base_ts = draw_ts - (offset_seconds * 1000)
            calculated_first_seed = (base_ts // 1000) * 1000

            match = "✅" if calculated_first_seed == actual_first_seed else "❌"
            print(f"   期号{record['period']}: 计算={calculated_first_seed}, 实际={actual_first_seed} {match}")

if __name__ == "__main__":
    analyze_seed_pattern()
