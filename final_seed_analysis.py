#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终种子分析工具
重新分析时间戳的单位和关系
"""

import csv
import re
from datetime import datetime

def parse_csv_line(line: str):
    """解析CSV行"""
    fields = []
    current_field = ""
    in_quotes = False
    bracket_count = 0
    
    i = 0
    while i < len(line):
        char = line[i]
        
        if char == '"' and (i == 0 or line[i-1] != '\\'):
            in_quotes = not in_quotes
            current_field += char
        elif char == '[':
            bracket_count += 1
            current_field += char
        elif char == ']':
            bracket_count -= 1
            current_field += char
        elif char == ',' and not in_quotes and bracket_count == 0:
            fields.append(current_field.strip())
            current_field = ""
        else:
            current_field += char
        
        i += 1
    
    if current_field:
        fields.append(current_field.strip())
    
    return fields

def parse_list_field(field_str: str):
    """解析列表字段"""
    try:
        field_str = field_str.strip('"')
        numbers = re.findall(r'\d+', field_str)
        return [int(num) for num in numbers]
    except:
        return []

def analyze_timestamp_units():
    """分析时间戳单位"""
    print("🔍 最终种子分析 - 时间戳单位分析")
    print("=" * 60)
    
    # 加载数据
    csv.field_size_limit(2000000)
    
    records = []
    with open('real_time_data_20250817.csv', 'r', encoding='utf-8') as f:
        content = f.read()
        lines = content.strip().split('\n')
        
        for i, line in enumerate(lines[1:3], 1):  # 只分析前2条记录
            try:
                fields = parse_csv_line(line)
                
                if len(fields) >= 7:
                    period = fields[1]
                    draw_timestamp = int(fields[2])
                    output_room = int(fields[6])
                    
                    timestamp_str = fields[3]
                    timestamp_list = parse_list_field(timestamp_str)
                    
                    if timestamp_list:
                        records.append({
                            'period': period,
                            'draw_timestamp': draw_timestamp,
                            'first_seed': timestamp_list[0],
                            'output_room': output_room,
                            'timestamp_list': timestamp_list[:5]  # 只取前5个
                        })
                        
            except Exception as e:
                print(f"跳过第{i}行: {e}")
                continue
    
    print(f"✅ 加载了 {len(records)} 条记录\n")
    
    # 分析时间戳单位
    for i, record in enumerate(records, 1):
        print(f"📊 记录 {i} (期号: {record['period']}):")
        
        draw_ts = record['draw_timestamp']
        first_seed = record['first_seed']
        
        print(f"   开奖时间戳: {draw_ts}")
        print(f"   首个种子: {first_seed}")
        print(f"   开奖房间: {record['output_room']}")
        
        # 检查时间戳是否是秒级还是毫秒级
        print(f"   时间戳分析:")
        
        # 假设开奖时间戳是秒级
        if draw_ts < 2000000000:  # 2033年之前，可能是秒级
            print(f"     开奖时间戳可能是秒级")
            draw_ts_ms = draw_ts * 1000  # 转换为毫秒
            print(f"     转换为毫秒: {draw_ts_ms}")
            
            # 检查与首个种子的关系
            diff_ms = first_seed - draw_ts_ms
            diff_seconds = diff_ms / 1000
            print(f"     首个种子 - 开奖时间戳(毫秒): {diff_ms} 毫秒")
            print(f"     差值(秒): {diff_seconds}")
            
            # 检查是否是简单的偏移
            if abs(diff_seconds) < 10:  # 偏移在10秒以内
                print(f"     🎯 发现简单偏移: {diff_seconds:.3f}秒")
                
                # 检查是否是向前偏移并去毫秒
                if diff_seconds < 0:  # 首个种子早于开奖时间
                    seconds_back = abs(diff_seconds)
                    print(f"     向前偏移: {seconds_back:.3f}秒")
                    
                    # 验证公式: 开奖时间戳(毫秒) - 偏移秒数*1000，然后去毫秒
                    test_base = draw_ts_ms - (round(seconds_back) * 1000)
                    test_base_no_ms = (test_base // 1000) * 1000
                    
                    print(f"     测试公式:")
                    print(f"       开奖时间戳(毫秒): {draw_ts_ms}")
                    print(f"       向前偏移{round(seconds_back)}秒: {draw_ts_ms} - {round(seconds_back)*1000} = {test_base}")
                    print(f"       去毫秒: {test_base_no_ms}")
                    print(f"       实际首个种子: {first_seed}")
                    
                    if test_base_no_ms == first_seed:
                        print(f"     ✅ 公式验证成功!")
                        return {
                            'formula_type': 'seconds_back_no_ms',
                            'offset_seconds': round(seconds_back),
                            'verified': True
                        }
        
        # 假设开奖时间戳是毫秒级
        else:
            print(f"     开奖时间戳可能是毫秒级")
            diff_ms = first_seed - draw_ts
            diff_seconds = diff_ms / 1000
            print(f"     首个种子 - 开奖时间戳: {diff_ms} 毫秒")
            print(f"     差值(秒): {diff_seconds}")
        
        # 转换为可读时间
        try:
            if draw_ts < 2000000000:  # 秒级
                dt = datetime.fromtimestamp(draw_ts)
                print(f"   开奖时间(秒级): {dt}")
            else:  # 毫秒级
                dt = datetime.fromtimestamp(draw_ts / 1000)
                print(f"   开奖时间(毫秒级): {dt}")
            
            # 首个种子时间
            dt_seed = datetime.fromtimestamp(first_seed / 1000)
            print(f"   首个种子时间: {dt_seed}")
            
        except:
            print(f"   时间转换失败")
        
        print(f"   种子序列前5个: {record['timestamp_list']}")
        print()
    
    return None

def main():
    """主函数"""
    result = analyze_timestamp_units()
    
    if result and result['verified']:
        print("🎉 发现种子计算公式!")
        print("=" * 40)
        
        offset = result['offset_seconds']
        print(f"""
🧮 种子计算公式:
===============
def calculate_first_seed(draw_timestamp_seconds):
    # 开奖时间戳转换为毫秒
    draw_timestamp_ms = draw_timestamp_seconds * 1000
    
    # 向前偏移 {offset} 秒
    base_timestamp = draw_timestamp_ms - ({offset} * 1000)
    
    # 去掉毫秒部分
    first_seed = (base_timestamp // 1000) * 1000
    
    return first_seed

def calculate_seed_sequence(draw_timestamp_seconds):
    first_seed = calculate_first_seed(draw_timestamp_seconds)
    return [first_seed + i for i in range(9000)]

# 使用示例:
# draw_timestamp = 1755424371  # 开奖时间戳(秒)
# seeds = calculate_seed_sequence(draw_timestamp)
# 然后使用 Unity Random 算法:
# for seed in seeds:
#     random.seed(seed)
#     result = random.randint(1, 8)
""")
    else:
        print("❌ 未能确定种子计算公式，需要进一步分析")

if __name__ == "__main__":
    main()
