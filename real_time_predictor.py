#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时预测工具
用于游戏过程中的实时号码预测和避开建议
"""

import time
import json
from datetime import datetime
from prediction_strategy import PredictionStrategy

class RealTimePredictor:
    """实时预测器"""
    
    def __init__(self):
        self.strategy = PredictionStrategy()
        self.prediction_history = []
    
    def predict_for_timestamp(self, timestamp_ms: int, strategy_type: str = 'primary') -> dict:
        """
        为指定时间戳生成预测
        
        Args:
            timestamp_ms: 毫秒级时间戳
            strategy_type: 策略类型
            
        Returns:
            预测结果字典
        """
        result = self.strategy.get_avoidance_numbers(timestamp_ms, strategy_type)
        
        # 添加时间信息
        result['datetime'] = datetime.fromtimestamp(timestamp_ms / 1000).strftime('%Y-%m-%d %H:%M:%S')
        result['prediction_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 保存到历史记录
        self.prediction_history.append(result)
        
        return result
    
    def predict_current_time(self, strategy_type: str = 'primary') -> dict:
        """
        为当前时间生成预测
        
        Args:
            strategy_type: 策略类型
            
        Returns:
            预测结果字典
        """
        current_timestamp = int(time.time() * 1000)
        return self.predict_for_timestamp(current_timestamp, strategy_type)
    
    def batch_predict_range(self, base_timestamp: int, offset_range: int = 100, 
                           strategy_type: str = 'primary') -> list:
        """
        批量预测时间戳范围
        
        Args:
            base_timestamp: 基础时间戳
            offset_range: 偏移范围
            strategy_type: 策略类型
            
        Returns:
            预测结果列表
        """
        results = []
        
        for offset in range(-offset_range, offset_range + 1):
            timestamp = base_timestamp + offset
            result = self.predict_for_timestamp(timestamp, strategy_type)
            results.append(result)
        
        return results
    
    def analyze_prediction_consensus(self, predictions: list) -> dict:
        """
        分析多个预测的共识
        
        Args:
            predictions: 预测结果列表
            
        Returns:
            共识分析结果
        """
        avoid_counts = {}
        safe_counts = {}
        total_predictions = len(predictions)
        
        # 统计每个号码被避开和被认为安全的次数
        for pred in predictions:
            for num in pred['avoid_numbers']:
                avoid_counts[num] = avoid_counts.get(num, 0) + 1
            for num in pred['safe_numbers']:
                safe_counts[num] = safe_counts.get(num, 0) + 1
        
        # 计算共识度
        consensus = {
            'total_predictions': total_predictions,
            'high_risk_numbers': {},  # 高风险号码（经常被避开）
            'low_risk_numbers': {},   # 低风险号码（经常被认为安全）
            'consensus_avoid': [],    # 共识避开号码
            'consensus_safe': [],     # 共识安全号码
        }
        
        # 分析高风险号码
        for num, count in avoid_counts.items():
            risk_percentage = (count / total_predictions) * 100
            consensus['high_risk_numbers'][num] = risk_percentage
            if risk_percentage >= 70:  # 70%以上的预测都建议避开
                consensus['consensus_avoid'].append(num)
        
        # 分析低风险号码
        for num, count in safe_counts.items():
            safe_percentage = (count / total_predictions) * 100
            consensus['low_risk_numbers'][num] = safe_percentage
            if safe_percentage >= 70:  # 70%以上的预测都认为安全
                consensus['consensus_safe'].append(num)
        
        return consensus
    
    def format_consensus_report(self, consensus: dict) -> str:
        """
        格式化共识分析报告
        
        Args:
            consensus: 共识分析结果
            
        Returns:
            格式化的报告字符串
        """
        report = []
        report.append("=" * 60)
        report.append("📊 多时间戳预测共识分析")
        report.append("=" * 60)
        
        report.append(f"📈 分析样本数: {consensus['total_predictions']} 个预测")
        report.append("")
        
        report.append("🚨 高风险号码分析:")
        if consensus['high_risk_numbers']:
            for num, risk in sorted(consensus['high_risk_numbers'].items(), 
                                  key=lambda x: x[1], reverse=True):
                report.append(f"   号码 {num}: {risk:.1f}% 的预测建议避开")
        else:
            report.append("   无明显高风险号码")
        report.append("")
        
        report.append("✅ 低风险号码分析:")
        if consensus['low_risk_numbers']:
            for num, safe in sorted(consensus['low_risk_numbers'].items(), 
                                  key=lambda x: x[1], reverse=True):
                report.append(f"   号码 {num}: {safe:.1f}% 的预测认为安全")
        else:
            report.append("   无明显低风险号码")
        report.append("")
        
        report.append("🎯 共识建议:")
        if consensus['consensus_avoid']:
            report.append(f"   强烈建议避开: {sorted(consensus['consensus_avoid'])}")
        if consensus['consensus_safe']:
            report.append(f"   相对安全选择: {sorted(consensus['consensus_safe'])}")
        
        if not consensus['consensus_avoid'] and not consensus['consensus_safe']:
            report.append("   预测结果分歧较大，建议谨慎投注")
        
        report.append("=" * 60)
        
        return "\n".join(report)
    
    def save_predictions_to_file(self, filename: str = None) -> str:
        """
        保存预测历史到文件
        
        Args:
            filename: 文件名，如果为None则自动生成
            
        Returns:
            保存的文件名
        """
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"prediction_history_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.prediction_history, f, ensure_ascii=False, 
                     indent=2, default=str)
        
        return filename
    
    def interactive_mode(self):
        """交互式预测模式"""
        print("🎮 实时预测工具 - 交互模式")
        print("=" * 50)
        print("命令说明:")
        print("  1 - 当前时间预测（主要策略）")
        print("  2 - 当前时间预测（辅助策略）")
        print("  3 - 当前时间预测（组合策略）")
        print("  4 - 自定义时间戳预测")
        print("  5 - 批量时间范围预测")
        print("  6 - 查看预测历史")
        print("  7 - 保存预测历史")
        print("  q - 退出")
        print("=" * 50)
        
        while True:
            try:
                choice = input("\n请选择操作 (1-7, q): ").strip().lower()
                
                if choice == 'q':
                    print("👋 感谢使用，再见！")
                    break
                
                elif choice == '1':
                    result = self.predict_current_time('primary')
                    print(self.strategy.format_strategy_report(result))
                
                elif choice == '2':
                    result = self.predict_current_time('secondary')
                    print(self.strategy.format_strategy_report(result))
                
                elif choice == '3':
                    result = self.predict_current_time('combined')
                    print(self.strategy.format_strategy_report(result))
                
                elif choice == '4':
                    timestamp_input = input("请输入时间戳（毫秒）: ").strip()
                    try:
                        timestamp = int(timestamp_input)
                        strategy_type = input("选择策略 (primary/secondary/combined): ").strip()
                        if strategy_type not in ['primary', 'secondary', 'combined']:
                            strategy_type = 'primary'
                        result = self.predict_for_timestamp(timestamp, strategy_type)
                        print(self.strategy.format_strategy_report(result))
                    except ValueError:
                        print("❌ 时间戳格式错误")
                
                elif choice == '5':
                    try:
                        base_ts = int(input("请输入基础时间戳（毫秒）: ").strip())
                        range_size = int(input("请输入范围大小（默认100）: ").strip() or "100")
                        strategy_type = input("选择策略 (primary/secondary/combined): ").strip()
                        if strategy_type not in ['primary', 'secondary', 'combined']:
                            strategy_type = 'primary'
                        
                        print(f"🔄 正在分析 {range_size*2+1} 个时间戳...")
                        predictions = self.batch_predict_range(base_ts, range_size, strategy_type)
                        consensus = self.analyze_prediction_consensus(predictions)
                        print(self.format_consensus_report(consensus))
                    except ValueError:
                        print("❌ 输入格式错误")
                
                elif choice == '6':
                    if self.prediction_history:
                        print(f"\n📋 预测历史 (共 {len(self.prediction_history)} 条):")
                        for i, pred in enumerate(self.prediction_history[-5:], 1):  # 显示最近5条
                            print(f"  {i}. {pred['datetime']} - 避开: {sorted(list(pred['avoid_numbers']))}")
                    else:
                        print("📋 暂无预测历史")
                
                elif choice == '7':
                    if self.prediction_history:
                        filename = self.save_predictions_to_file()
                        print(f"💾 预测历史已保存到: {filename}")
                    else:
                        print("📋 暂无预测历史可保存")
                
                else:
                    print("❌ 无效选择，请重新输入")
                    
            except KeyboardInterrupt:
                print("\n👋 用户中断，退出程序")
                break
            except Exception as e:
                print(f"❌ 发生错误: {e}")

def main():
    """主函数"""
    predictor = RealTimePredictor()
    predictor.interactive_mode()

if __name__ == "__main__":
    main()
