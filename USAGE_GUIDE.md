# 使用指南

## 🚀 快速开始

### 前置条件
- Python 3.7+
- 数据文件: `real_time_data_20250817.csv`

### 运行步骤

#### 1. 索引位置准确率统计
```bash
python sorted_index_accuracy.py
```

**输出结果:**
```
=== 按准确率排序的索引位置统计 ===
总记录数: 153
有效记录数: 152

=== 各索引位置准确率统计（按准确率排序）===
排名 | 索引 | 总出现次数 | 命中次数 | 准确率
--------------------------------------------------
  1  | 1145 |     152      |    35    |  23.03%
  2  | 4692 |     152      |    34    |  22.37%
  3  | 7739 |     152      |    34    |  22.37%
  ...

=== 关键发现 ===
准确率最高的索引: 1145 (23.03%)
准确率最低的索引: 6940 (2.63%)
```

**生成文件:** `sorted_index_accuracy_report.txt`

#### 2. 索引区域准确率统计
```bash
python flexible_region_analysis.py
```

**输出结果:**
```
=== 比较不同区域大小的效果 ===
区域大小 | 总区域数 | 最高准确率 | 最低准确率 | 平均准确率 | 标准差
----------------------------------------------------------------------
    5    |   1800   |    61.84%   |    35.53%   |    48.82%   |   4.03%
   10    |    900   |    85.53%   |    61.84%   |    73.82%   |   3.67%
   15    |    600   |    93.42%   |    78.29%   |    86.60%   |   2.55%
   ...

=== 推荐区域大小 ===
建议使用区域大小: 5
理由: 标准差最大(4.03%)，能更好地区分不同区域的表现
```

**生成文件:** 
- `region_size_5_accuracy_report.txt` (推荐)
- `region_size_10_accuracy_report.txt`
- `region_size_15_accuracy_report.txt`
- `region_size_20_accuracy_report.txt`
- `region_size_25_accuracy_report.txt`
- `region_size_30_accuracy_report.txt`

---

## 📊 结果解读

### 索引位置统计结果
- **高价值索引**: 准确率 > 20%
- **优秀索引**: 准确率 15% - 20%
- **平均水平**: 准确率 10% - 15%
- **低效索引**: 准确率 < 10%

### 区域统计结果
- **推荐使用**: 区域大小 = 5
- **高价值区域**: 准确率 > 55%
- **优秀区域**: 准确率 45% - 55%
- **平均区域**: 准确率 40% - 45%
- **低效区域**: 准确率 < 40%

---

## 🎯 实际应用

### 策略优化建议

#### 基于索引位置统计
1. **重点关注**: 排名前100的索引位置
2. **权重调整**: 给高准确率索引分配更高权重
3. **过滤策略**: 忽略准确率<5%的索引位置

#### 基于区域统计
1. **区域选择**: 优先选择准确率>50%的区域
2. **组合策略**: 结合多个高价值区域
3. **动态调整**: 定期重新评估区域表现

### 监控指标
- **整体准确率**: 所有索引/区域的平均表现
- **稳定性**: 准确率的标准差
- **覆盖率**: 高价值索引/区域的数量占比

---

## ⚠️ 注意事项

### 数据要求
1. **文件格式**: CSV格式，UTF-8编码
2. **必需列**: 预测号码序列列、开出号码列
3. **数据完整性**: 确保预测序列长度一致

### 性能考虑
1. **内存使用**: 大文件处理时注意内存占用
2. **处理时间**: 9000个索引位置的统计需要一定时间
3. **磁盘空间**: 报告文件可能较大

### 结果可靠性
1. **样本数量**: 建议至少100条有效记录
2. **时间跨度**: 数据应覆盖足够的时间周期
3. **环境稳定**: 确保预测环境和开奖环境一致

---

## 🔧 自定义配置

### 修改区域大小
编辑 `flexible_region_analysis.py` 中的 `sizes_to_test` 列表:
```python
sizes_to_test = [3, 5, 8, 10, 12, 15]  # 自定义区域大小
```

### 调整显示数量
修改脚本中的显示参数:
```python
# 显示前N名结果
for rank, item in enumerate(region_accuracies[:N], 1):
```

### 更改准确率区间
修改准确率区间定义:
```python
ranges = [
    (25, float('inf'), "25%以上"),  # 自定义区间
    (20, 25, "20%-25%"),
    (15, 20, "15%-20%"),
    # ...
]
```

---

## 📞 故障排除

### 常见问题

#### 1. 文件读取错误
```
错误: FileNotFoundError: real_time_data_20250817.csv
解决: 确保CSV文件在当前目录下
```

#### 2. 内存不足
```
错误: MemoryError
解决: 增加系统内存或分批处理数据
```

#### 3. 数据格式错误
```
错误: 预测号码解析失败
解决: 检查CSV文件格式，确保预测序列为有效的Python列表格式
```

#### 4. 编码问题
```
错误: UnicodeDecodeError
解决: 确保CSV文件为UTF-8编码
```

### 调试模式
在脚本开头添加调试信息:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

---

## 📈 性能优化

### 大数据处理
1. **分批处理**: 将大文件分割成小批次
2. **并行计算**: 使用多进程处理不同区域
3. **内存优化**: 及时释放不需要的数据

### 结果缓存
1. **中间结果**: 保存计算中间结果
2. **增量更新**: 只处理新增数据
3. **结果复用**: 避免重复计算

---

## 📋 版本历史

- **v2.0**: 当前版本，包含两个核心统计模式
- **v1.x**: 历史版本，已废弃

---

## 🤝 贡献指南

欢迎提交改进建议和bug报告！

### 代码规范
- 遵循PEP 8编码规范
- 添加详细的函数注释
- 保持代码简洁易读

### 测试要求
- 提供测试数据
- 验证结果准确性
- 确保向后兼容性
