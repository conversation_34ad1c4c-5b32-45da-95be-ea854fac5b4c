#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单种子准确率测试
测试单个种子位置的实际预测准确率
"""

import csv
import re
import random
from collections import Counter
from typing import List, Dict, Tuple

class SingleSeedAccuracyTester:
    """单种子准确率测试器"""
    
    def __init__(self):
        self.lottery_data = []
    
    def load_lottery_data(self, csv_file: str) -> bool:
        """加载开奖数据"""
        try:
            csv.field_size_limit(2000000)
            
            with open(csv_file, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.strip().split('\n')
                
                for i, line in enumerate(lines[1:], 1):
                    try:
                        fields = self._parse_csv_line(line)
                        
                        if len(fields) >= 7:
                            period = fields[1]
                            draw_timestamp = int(fields[2])
                            output_room = int(fields[6])
                            
                            record = {
                                'period': period,
                                'draw_timestamp_seconds': draw_timestamp,
                                'actual_output_room': output_room
                            }
                            
                            self.lottery_data.append(record)
                        
                    except Exception as e:
                        continue
            
            print(f"✅ 成功加载 {len(self.lottery_data)} 条开奖记录")
            return len(self.lottery_data) > 0
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return False
    
    def _parse_csv_line(self, line: str) -> List[str]:
        """解析CSV行"""
        fields = []
        current_field = ""
        in_quotes = False
        bracket_count = 0
        
        i = 0
        while i < len(line):
            char = line[i]
            
            if char == '"' and (i == 0 or line[i-1] != '\\'):
                in_quotes = not in_quotes
                current_field += char
            elif char == '[':
                bracket_count += 1
                current_field += char
            elif char == ']':
                bracket_count -= 1
                current_field += char
            elif char == ',' and not in_quotes and bracket_count == 0:
                fields.append(current_field.strip())
                current_field = ""
            else:
                current_field += char
            
            i += 1
        
        if current_field:
            fields.append(current_field.strip())
        
        return fields
    
    def generate_random_sequence(self, draw_timestamp_seconds: int) -> List[int]:
        """生成随机数序列"""
        first_seed = (draw_timestamp_seconds * 1000) - 5000
        
        random_sequence = []
        for i in range(9000):
            seed = first_seed + i
            random.seed(seed)
            result = random.randint(1, 8)
            random_sequence.append(result)
        
        return random_sequence
    
    def test_single_position_accuracy(self, position: int) -> Dict:
        """测试单个位置的准确率"""
        successful_predictions = 0
        total_predictions = len(self.lottery_data)
        prediction_details = []
        
        for record in self.lottery_data:
            # 生成随机数序列
            random_sequence = self.generate_random_sequence(record['draw_timestamp_seconds'])
            
            # 获取指定位置的预测
            if position < len(random_sequence):
                predicted_number = random_sequence[position]
                actual_number = record['actual_output_room']
                
                is_correct = predicted_number == actual_number
                if is_correct:
                    successful_predictions += 1
                
                prediction_details.append({
                    'period': record['period'],
                    'predicted': predicted_number,
                    'actual': actual_number,
                    'correct': is_correct
                })
        
        accuracy_rate = (successful_predictions / total_predictions * 100) if total_predictions > 0 else 0
        
        return {
            'position': position,
            'successful_predictions': successful_predictions,
            'total_predictions': total_predictions,
            'accuracy_rate': accuracy_rate,
            'prediction_details': prediction_details
        }
    
    def test_multiple_positions(self, positions: List[int]) -> Dict:
        """测试多个位置的准确率"""
        results = {}
        
        print(f"🔍 测试 {len(positions)} 个单种子位置的准确率...")
        
        for i, position in enumerate(positions, 1):
            print(f"测试进度: {i}/{len(positions)} (位置{position})", end='\r')
            
            result = self.test_single_position_accuracy(position)
            results[position] = result
        
        print()  # 换行
        
        return results
    
    def find_best_single_positions(self, sample_positions: int = 100) -> List[Dict]:
        """寻找最佳单种子位置"""
        print(f"🎯 搜索最佳单种子位置 (采样{sample_positions}个位置)...")
        
        # 在9000个位置中均匀采样
        step = 9000 // sample_positions
        test_positions = list(range(0, 9000, step))[:sample_positions]
        
        results = self.test_multiple_positions(test_positions)
        
        # 按准确率排序
        sorted_results = sorted(results.items(), key=lambda x: x[1]['accuracy_rate'], reverse=True)
        
        return [result[1] for result in sorted_results]
    
    def analyze_prediction_distribution(self, position: int) -> Dict:
        """分析单个位置的预测分布"""
        predicted_numbers = []
        
        for record in self.lottery_data:
            random_sequence = self.generate_random_sequence(record['draw_timestamp_seconds'])
            if position < len(random_sequence):
                predicted_numbers.append(random_sequence[position])
        
        # 统计分布
        distribution = Counter(predicted_numbers)
        total_predictions = len(predicted_numbers)
        
        distribution_percentages = {
            number: count / total_predictions * 100 
            for number, count in distribution.items()
        }
        
        return {
            'position': position,
            'distribution': dict(distribution),
            'distribution_percentages': distribution_percentages,
            'total_predictions': total_predictions,
            'unique_numbers': len(distribution),
            'most_common': distribution.most_common(1)[0] if distribution else (0, 0),
            'least_common': distribution.most_common()[-1] if distribution else (0, 0)
        }
    
    def generate_accuracy_report(self, results: Dict) -> str:
        """生成准确率报告"""
        report = []
        report.append("🎯 单种子准确率测试报告")
        report.append("=" * 60)
        report.append(f"📊 测试记录数: {len(self.lottery_data)}")
        report.append(f"🔍 测试位置数: {len(results)}")
        report.append("")
        
        # 统计准确率分布
        accuracy_rates = [result['accuracy_rate'] for result in results.values()]
        
        if accuracy_rates:
            avg_accuracy = sum(accuracy_rates) / len(accuracy_rates)
            max_accuracy = max(accuracy_rates)
            min_accuracy = min(accuracy_rates)
            
            report.append("📈 准确率统计:")
            report.append(f"   平均准确率: {avg_accuracy:.2f}%")
            report.append(f"   最高准确率: {max_accuracy:.2f}%")
            report.append(f"   最低准确率: {min_accuracy:.2f}%")
            report.append("")
            
            # 准确率分布
            accuracy_ranges = {
                '0-10%': 0, '10-20%': 0, '20-30%': 0, '30-40%': 0,
                '40-50%': 0, '50-60%': 0, '60-70%': 0, '70%+': 0
            }
            
            for acc in accuracy_rates:
                if acc < 10:
                    accuracy_ranges['0-10%'] += 1
                elif acc < 20:
                    accuracy_ranges['10-20%'] += 1
                elif acc < 30:
                    accuracy_ranges['20-30%'] += 1
                elif acc < 40:
                    accuracy_ranges['30-40%'] += 1
                elif acc < 50:
                    accuracy_ranges['40-50%'] += 1
                elif acc < 60:
                    accuracy_ranges['50-60%'] += 1
                elif acc < 70:
                    accuracy_ranges['60-70%'] += 1
                else:
                    accuracy_ranges['70%+'] += 1
            
            report.append("📊 准确率分布:")
            for range_name, count in accuracy_ranges.items():
                percentage = count / len(accuracy_rates) * 100
                report.append(f"   {range_name}: {count}个位置 ({percentage:.1f}%)")
            report.append("")
        
        # 显示最佳位置
        sorted_results = sorted(results.items(), key=lambda x: x[1]['accuracy_rate'], reverse=True)
        
        report.append("🏆 最佳单种子位置 (前10个):")
        for i, (position, result) in enumerate(sorted_results[:10], 1):
            report.append(f"   {i:2d}. 位置{position:4d}: {result['accuracy_rate']:5.1f}% "
                         f"({result['successful_predictions']}/{result['total_predictions']})")
        
        report.append("")
        
        # 理论分析
        theoretical_accuracy = 100 / 8  # 理论上随机选择的准确率
        report.append("🧮 理论分析:")
        report.append(f"   理论随机准确率: {theoretical_accuracy:.2f}%")
        
        if accuracy_rates:
            if max_accuracy > theoretical_accuracy * 1.5:
                report.append("   ✅ 发现显著高于随机的位置")
            elif avg_accuracy > theoretical_accuracy * 1.1:
                report.append("   ⚠️ 平均准确率略高于随机")
            else:
                report.append("   ❌ 准确率接近随机水平")
        
        report.append("=" * 60)
        
        return "\n".join(report)

def main():
    """主函数"""
    print("🎯 单种子准确率测试")
    print("=" * 50)
    print("测试单个种子位置的实际预测准确率")
    print()
    
    # 创建测试器
    tester = SingleSeedAccuracyTester()
    
    # 加载数据
    if not tester.load_lottery_data('real_time_data_20250817.csv'):
        return
    
    print()
    
    # 测试特定位置
    print("🔍 测试特定单种子位置:")
    print("-" * 40)
    
    specific_positions = [0, 500, 1000, 2000, 2276, 4362, 6000, 8000]
    specific_results = tester.test_multiple_positions(specific_positions)
    
    for position in specific_positions:
        result = specific_results[position]
        print(f"位置 {position:4d}: {result['accuracy_rate']:5.1f}% "
              f"({result['successful_predictions']}/{result['total_predictions']})")
    
    print()
    
    # 寻找最佳位置
    best_results = tester.find_best_single_positions(50)  # 测试50个位置
    
    # 生成报告
    all_results = {}
    for result in best_results:
        all_results[result['position']] = result
    
    report = tester.generate_accuracy_report(all_results)
    print(report)
    
    # 分析最佳位置的预测分布
    if best_results:
        best_position = best_results[0]['position']
        best_accuracy = best_results[0]['accuracy_rate']
        
        print(f"\n🔍 最佳位置 {best_position} 的预测分布分析:")
        print("-" * 50)
        
        distribution = tester.analyze_prediction_distribution(best_position)
        
        print(f"准确率: {best_accuracy:.1f}%")
        print(f"预测分布: {distribution['distribution']}")
        print(f"百分比分布:")
        for number, percentage in sorted(distribution['distribution_percentages'].items()):
            print(f"   房间{number}: {percentage:.1f}%")
        
        print(f"最常预测: 房间{distribution['most_common'][0]} ({distribution['most_common'][1]}次)")
        print(f"最少预测: 房间{distribution['least_common'][0]} ({distribution['least_common'][1]}次)")
    
    # 保存报告
    with open('single_seed_accuracy_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n💾 详细报告已保存到: single_seed_accuracy_report.txt")
    
    # 总结
    if best_results:
        best_accuracy = best_results[0]['accuracy_rate']
        theoretical = 100 / 8
        
        print(f"\n🎯 总结:")
        print(f"最佳单种子准确率: {best_accuracy:.1f}%")
        print(f"理论随机准确率: {theoretical:.1f}%")
        
        if best_accuracy > theoretical * 1.5:
            print("✅ 单种子预测显著优于随机!")
        elif best_accuracy > theoretical * 1.1:
            print("⚠️ 单种子预测略优于随机")
        else:
            print("❌ 单种子预测接近随机水平")

if __name__ == "__main__":
    main()
